import { NextRequest, NextResponse } from 'next/server';
import { Pool } from 'pg';

// Configuração do banco de dados PostgreSQL
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Rota para buscar uma família específica pelo ID
export async function GET(req: NextRequest, context: { params: { id: string } }) {
  const { params } = context;
  const { id: familyId } = await params;

  if (!familyId) {
    return NextResponse.json({ error: 'ID da família não fornecido' }, { status: 400 });
  }  try {
    const result = await pool.query('SELECT id, title, file_path, image_path, description, room, category, tags, manufacturer, minimal_revit_version, price, premium, product_link, key_features, detailed_description, detailed_specification, created_at FROM families WHERE id = $1', [familyId]);
    
    if (result.rows.length === 0) {
      return NextResponse.json({ error: '<PERSON><PERSON><PERSON><PERSON> não encontrada' }, { status: 404 });
    }
    
    const family = result.rows[0];
    const responseFamily = {
      id: family.id,
      title: family.title,
      image_url: family.image_path ? `https://pub-93d9750e41f7494db2c406c9e858c9e6.r2.dev/${family.image_path}` : null,
      file_url: family.file_path ? `https://pub-93d9750e41f7494db2c406c9e858c9e6.r2.dev/${family.file_path}` : null,
      created_at: family.created_at,
      description: family.description,
      room: family.room,
      category: family.category,
      tags: family.tags,
      manufacturer: family.manufacturer,
      minimal_revit_version: family.minimal_revit_version,
      price: family.price,
      premium: family.premium,
      product_link: family.product_link,
      key_features: family.key_features,
      detailed_description: family.detailed_description,
      detailed_specification: family.detailed_specification
    };

    return NextResponse.json(responseFamily);
  } catch (error) {
    console.error(`Erro ao buscar família com ID ${familyId}:`, error);
    return NextResponse.json({ error: 'Erro ao buscar família' }, { status: 500 });
  }
}
