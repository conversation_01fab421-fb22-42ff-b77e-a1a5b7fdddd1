import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { NextRequest, NextResponse } from 'next/server';

const MODEL_NAME = 'gemini-2.0-flash-exp'; // Updated to use the newer 2.0 flash model
const API_KEY = process.env.GEMINI_API_KEY;

if (!API_KEY) {
  console.error("GEMINI_API_KEY is not set in environment variables.");
}

async function fileToGenerativePart(file: File) {
  const base64EncodedData = Buffer.from(await file.arrayBuffer()).toString('base64');
  return {
    inlineData: {
      data: base64EncodedData,
      mimeType: file.type,
    },
  };
}

export async function POST(req: NextRequest) {
  if (!API_KEY) {
    return NextResponse.json({ error: 'API Key not configured.' }, { status: 500 });
  }

  try {
    const formData = await req.formData();
    const imageFile = formData.get('image') as File | null;

    if (!imageFile) {
      return NextResponse.json({ error: 'No image file provided.' }, { status: 400 });
    }

    const genAI = new GoogleGenerativeAI(API_KEY);
    const model = genAI.getGenerativeModel({ model: MODEL_NAME });

    const imagePart = await fileToGenerativePart(imageFile);    const prompt = `
Analyze this image and generate the following information in JSON format for a Revit plugin marketplace:

1. Titulo: voce é um Designer de luxo, ao receber a imagem, identifique o objeto (ex: cadeira), crie um título exclusivo de 1–3 palavras em francês/italiano/latim com 50% de 1 palavra, 40% de 2 palavras e 10% de 3 palavras e retorne “<objeto> <Título>” sem explicações.
2. Description: A brief description of the object in Portuguese (1-2 sentences)
3. Rooms: List of suitable rooms/environments where this object would be used (select from the available options)
4. Category: The most appropriate category for this object (select from the available options)

Available Rooms (select multiple if applicable):
- Quarto
- Closet
- Banheiro
- Sala de Estar
- Sala de Jantar
- Cozinha
- Despensa
- Área de serviço
- Área de lazer
- Jardim
- Corredor
- Sacada
- Escritorio

Available Categories (select one):
- arquitetonico
- estrutural
- hidrossanitario
- eletrico
- iluminacao
- design_de_interiores
- protecao_contra_incendio
- protecao_contra_descargas_atmosfericas
- sinalizacao
- automacao
- paisagismo
- drenagem_pluvial
- acessibilidade
- seguranca_no_trabalho

Respond with ONLY a valid JSON object in this exact format:
{
  "title": "Product Title",
  "description": "Brief description of the object",
  "rooms": ["Room1", "Room2"],
  "category": "category_value"
}

Focus on the main object/product visible in the image. Use proper Portuguese grammar.
`;    const generationConfig = {
      temperature: 0.3, // Lower temperature for more deterministic/factual responses
      topK: 20,
      topP: 0.8,
      maxOutputTokens: 200, // Increased for JSON response with multiple fields
    };

    const safetySettings = [
      { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
      { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
      { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
      { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
    ];    const result = await model.generateContent({
      contents: [{ role: "user", parts: [imagePart, {text: prompt}] }],
      generationConfig,
      safetySettings,
    });

    // Check for safety issues
    if (result.response.promptFeedback && result.response.promptFeedback.blockReason) {
        console.error('Gemini API blocked the prompt:', result.response.promptFeedback);
        return NextResponse.json({ error: `Geração de título bloqueada por segurança: ${result.response.promptFeedback.blockReason}` }, { status: 400 });
    }
    
    // Check if the response was blocked for safety
    const candidate = result.response.candidates?.[0];
    if (candidate?.finishReason === 'SAFETY') {
        console.error('Response blocked for safety reasons');
        return NextResponse.json({ error: 'Geração de título bloqueada por políticas de segurança' }, { status: 400 });
    }    // Extract the generated text
    if (candidate && candidate.content && candidate.content.parts && candidate.content.parts[0] && candidate.content.parts[0].text) {
      let responseText = candidate.content.parts[0].text.trim();
      
      // Clean up the response (remove markdown code blocks if present)
      responseText = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '');
      responseText = responseText.trim();
      
      // Try to parse as JSON
      try {
        const parsedResponse = JSON.parse(responseText);
        
        // Validate required fields
        if (!parsedResponse.title || !parsedResponse.description || !parsedResponse.rooms || !parsedResponse.category) {
          console.error('Missing required fields in AI response:', parsedResponse);
          return NextResponse.json({ error: 'Resposta da IA incompleta' }, { status: 500 });
        }
        
        // Clean up the title
        let cleanTitle = parsedResponse.title.replace(/["'`*#\n\r]+/g, '').trim();
        if (cleanTitle.length === 0) {
          return NextResponse.json({ error: 'Título gerado está vazio após limpeza' }, { status: 500 });
        }
        
        return NextResponse.json({
          title: cleanTitle,
          description: parsedResponse.description,
          rooms: Array.isArray(parsedResponse.rooms) ? parsedResponse.rooms : [parsedResponse.rooms],
          category: parsedResponse.category
        });
        
      } catch (parseError) {
        console.error('Failed to parse AI response as JSON:', responseText);
        console.error('Parse error:', parseError);
        return NextResponse.json({ error: 'Resposta da IA não está em formato JSON válido' }, { status: 500 });
      }
    } else {
      console.error('No valid response generated by Gemini API. Response:', JSON.stringify(result.response, null, 2));
      return NextResponse.json({ error: 'Não foi possível gerar uma resposta válida com a IA' }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error calling Gemini API:', error);
    return NextResponse.json({ error: `Erro ao chamar a API da Gemini: ${error.message || error.toString()}` }, { status: 500 });
  }
}
