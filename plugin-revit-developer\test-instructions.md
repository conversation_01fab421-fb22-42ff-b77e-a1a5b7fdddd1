# Instruções de Teste - BIMEX Developer Plugin

## Pré-requisitos para Teste

1. **Revit 2024** instalado
2. **Plugin compilado e instalado** (use build.bat e install.bat)
3. **Arquivo de família de teste** (.rfa) disponível

## Procedimento de Teste

### 1. Verificar Instalação
- [ ] Abrir o Revit 2024
- [ ] Verificar se a aba "BIMEX Developer" aparece na ribbon
- [ ] Verificar se o painel "Family Tools" está visível
- [ ] Verificar se o botão "Family Export" está presente

### 2. Testar Funcionalidade Family Export

#### Teste Básico
1. [ ] Clicar no botão "Family Export"
2. [ ] Verificar se um novo documento é criado automaticamente
3. [ ] Verificar se um piso 5x5 metros é criado na origem
4. [ ] Quando o seletor de arquivo abrir, escolher uma família .rfa válida
5. [ ] Verificar se a família é carregada na origem (0,0,0)
6. [ ] Verificar se o piso é removido automaticamente
7. [ ] Verificar se apenas a família permanece no modelo

#### Teste de Cancelamento
1. [ ] Clicar no botão "Family Export"
2. [ ] Quando o seletor de arquivo abrir, clicar em "Cancelar"
3. [ ] Verificar se o processo é cancelado corretamente
4. [ ] Verificar se uma mensagem de cancelamento é exibida

#### Teste com Arquivo Inválido
1. [ ] Clicar no botão "Family Export"
2. [ ] Selecionar um arquivo que não seja .rfa (ex: .txt, .dwg)
3. [ ] Verificar se uma mensagem de erro apropriada é exibida

### 3. Verificar Resultados

#### Modelo Criado
- [ ] O novo modelo contém apenas a família carregada
- [ ] A família está posicionada na origem (0,0,0)
- [ ] Não há piso no modelo final
- [ ] O modelo pode ser salvo normalmente

#### Interface do Usuário
- [ ] Todas as mensagens são claras e informativas
- [ ] Não há travamentos ou erros inesperados
- [ ] O plugin não interfere com outras funcionalidades do Revit

## Famílias de Teste Recomendadas

Use os seguintes tipos de família para teste:

1. **Mobiliário simples** (cadeira, mesa)
2. **Equipamento** (ar condicionado, luminária)
3. **Elemento estrutural** (viga, pilar)
4. **Família paramétrica** com múltiplos tipos

## Arquivo de Teste Sugerido

Use o arquivo de teste mencionado nas memórias:
- `C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa`

## Problemas Conhecidos e Soluções

### Plugin não aparece
- **Causa**: Arquivos não copiados corretamente
- **Solução**: Executar install.bat como administrador

### Erro ao criar piso
- **Causa**: Template sem tipo de piso padrão
- **Solução**: Usar template arquitetônico padrão

### Erro ao carregar família
- **Causa**: Arquivo de família corrompido ou inválido
- **Solução**: Usar família válida do Revit

## Log de Teste

Data: ___________
Testador: ___________
Versão do Revit: ___________

| Teste | Status | Observações |
|-------|--------|-------------|
| Instalação | ⬜ Pass / ⬜ Fail | |
| Criação de modelo | ⬜ Pass / ⬜ Fail | |
| Criação de piso | ⬜ Pass / ⬜ Fail | |
| Seleção de família | ⬜ Pass / ⬜ Fail | |
| Carregamento de família | ⬜ Pass / ⬜ Fail | |
| Remoção de piso | ⬜ Pass / ⬜ Fail | |
| Cancelamento | ⬜ Pass / ⬜ Fail | |
| Arquivo inválido | ⬜ Pass / ⬜ Fail | |

## Notas Adicionais

- Teste com diferentes tipos de família
- Verifique o comportamento com famílias grandes
- Teste em diferentes templates de projeto
- Verifique a performance com famílias complexas
