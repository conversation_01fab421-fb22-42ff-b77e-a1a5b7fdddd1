@echo off
echo ========================================
echo BIMEX Developer Plugin - Instalador
echo ========================================

REM Definir pasta de destino do Revit 2024
set "REVIT_ADDINS_PATH=%APPDATA%\Autodesk\Revit\Addins\2024"

REM Verificar se a pasta existe, se não, criar
if not exist "%REVIT_ADDINS_PATH%" (
    echo Criando pasta de Add-ins do Revit...
    mkdir "%REVIT_ADDINS_PATH%"
)

REM Verificar se os arquivos compilados existem
if not exist "bin\Release\net48\BimexDeveloperPlugin.dll" (
    echo ERRO: Plugin não compilado. Execute build.bat primeiro.
    pause
    exit /b 1
)

echo Copiando arquivos para a pasta de Add-ins do Revit...

REM Copiar DLL
copy "bin\Release\net48\BimexDeveloperPlugin.dll" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Falha ao copiar BimexDeveloperPlugin.dll
    pause
    exit /b 1
)

REM Copiar arquivo .addin
copy "BimexDeveloperPlugin.addin" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Falha ao copiar BimexDeveloperPlugin.addin
    pause
    exit /b 1
)

echo.
echo ========================================
echo Instalação concluída com sucesso!
echo ========================================
echo.
echo O plugin foi instalado em:
echo %REVIT_ADDINS_PATH%
echo.
echo Reinicie o Revit para ver a nova aba "BIMEX Developer"
echo.
pause
