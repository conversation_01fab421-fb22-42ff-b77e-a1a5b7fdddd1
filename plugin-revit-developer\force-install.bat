@echo off
echo ========================================
echo BIMEX Developer Plugin - Instalação Forçada
echo ========================================

REM Definir pasta de destino do Revit 2024
set "REVIT_ADDINS_PATH=%APPDATA%\Autodesk\Revit\Addins\2024"

echo ATENÇÃO: Feche o Revit antes de continuar!
echo.
pause

REM Verificar se os arquivos compilados existem
if not exist "bin\Release\net48\BimexDeveloperPlugin.dll" (
    echo ERRO: Plugin não compilado. Execute build.bat primeiro.
    pause
    exit /b 1
)

echo Tentando parar processos do Revit...
taskkill /f /im Revit.exe >nul 2>&1

echo Aguardando 3 segundos...
timeout /t 3 >nul

echo Copiando arquivos para a pasta de Add-ins do Revit...

REM Tentar copiar DLL com força
copy /Y "bin\Release\net48\BimexDeveloperPlugin.dll" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Ainda não foi possível copiar a DLL. 
    echo Certifique-se de que o Revit está fechado.
    pause
    exit /b 1
)

REM Copiar arquivo .addin
copy /Y "BimexDeveloperPlugin.addin" "%REVIT_ADDINS_PATH%\"
if %errorlevel% neq 0 (
    echo ERRO: Falha ao copiar BimexDeveloperPlugin.addin
    pause
    exit /b 1
)

echo.
echo ========================================
echo Instalação forçada concluída com sucesso!
echo ========================================
echo.
echo O plugin foi atualizado em:
echo %REVIT_ADDINS_PATH%
echo.
echo Agora você pode abrir o Revit e testar o plugin corrigido.
echo.
pause
