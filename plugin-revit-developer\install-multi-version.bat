@echo off
echo ========================================
echo BIMEX Developer Plugin - Instalador Multi-Versão
echo ========================================

REM Verificar se os arquivos compilados existem
if not exist "bin\Release\net48\BimexDeveloperPlugin.dll" (
    echo ERRO: Plugin não compilado. Execute build.bat primeiro.
    pause
    exit /b 1
)

echo Detectando versões do Revit instaladas...
echo.

REM Definir versões para verificar
set "versions=2023 2024 2025"

REM Contador de instalações
set "installed_count=0"

for %%v in (%versions%) do (
    set "REVIT_PATH=%APPDATA%\Autodesk\Revit\Addins\%%v"
    
    if exist "!REVIT_PATH!" (
        echo Revit %%v detectado - Instalando plugin...
        
        REM Copiar DLL
        copy "bin\Release\net48\BimexDeveloperPlugin.dll" "!REVIT_PATH!\" >nul
        
        REM Copiar arquivo .addin específico da versão se existir, senão usar o padrão
        if exist "config\BimexDeveloperPlugin-%%v.addin" (
            copy "config\BimexDeveloperPlugin-%%v.addin" "!REVIT_PATH!\BimexDeveloperPlugin.addin" >nul
        ) else (
            copy "BimexDeveloperPlugin.addin" "!REVIT_PATH!\" >nul
        )
        
        echo   ✓ Instalado em Revit %%v
        set /a "installed_count+=1"
    ) else (
        echo   ✗ Revit %%v não encontrado
    )
)

echo.
echo ========================================

if %installed_count% gtr 0 (
    echo Instalação concluída com sucesso!
    echo Plugin instalado em %installed_count% versão(ões) do Revit.
) else (
    echo Nenhuma versão do Revit foi encontrada.
    echo Verifique se o Revit está instalado corretamente.
)

echo ========================================
echo.
echo Reinicie o Revit para ver a nova aba "BIMEX Developer"
echo.
pause
