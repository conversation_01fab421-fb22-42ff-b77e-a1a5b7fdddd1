# BIMEX Developer Plugin - Documentação Técnica

## Arquitetura do Plugin

### Estrutura de Classes

```
BimexDeveloperPlugin
├── App (IExternalApplication)
│   ├── OnStartup() - Cria aba e botões
│   └── OnShutdown() - Limpeza
└── FamilyExportCommand (IExternalCommand)
    ├── Execute() - Método principal
    ├── CreateNewModel() - Cria novo documento
    ├── CreateFloor() - Cria piso 5x5
    ├── SelectFamilyFile() - Seletor de arquivo
    ├── LoadAndPlaceFamily() - Carrega família
    └── DeleteFloor() - Remove piso
```

### Fluxo de Execução

1. **Inicialização**
   - `App.OnStartup()` é chamado quando o Revit inicia
   - Cria aba "BIMEX Developer" na ribbon
   - Adiciona painel "Family Tools"
   - Registra comando "Family Export"

2. **Execução do Comando**
   - Usuário clica no botão "Family Export"
   - `FamilyExportCommand.Execute()` é chamado
   - Executa sequência de operações

3. **Operações Principais**
   ```
   CreateNewModel() → CreateFloor() → SelectFamilyFile() → LoadAndPlaceFamily() → DeleteFloor()
   ```

## Detalhes Técnicos

### Criação de Novo Modelo
```csharp
Document newDoc = uiApp.Application.NewProjectDocument(UnitSystem.Metric);
```
- Usa sistema métrico
- Baseado no template padrão do Revit

### Criação do Piso 5x5
```csharp
// Pontos do retângulo 5x5 metros
List<XYZ> points = new List<XYZ>
{
    new XYZ(-2.5, -2.5, 0),  // Canto inferior esquerdo
    new XYZ(2.5, -2.5, 0),   // Canto inferior direito
    new XYZ(2.5, 2.5, 0),    // Canto superior direito
    new XYZ(-2.5, 2.5, 0)    // Canto superior esquerdo
};
```

### Posicionamento da Família
- Família é sempre posicionada na origem (0,0,0)
- Usa o nível ativo do documento
- Tipo estrutural: NonStructural

## Tratamento de Erros

### Tipos de Erro Tratados

1. **Erro na criação do modelo**
   - Causa: Template não disponível
   - Tratamento: Retorna Result.Failed

2. **Erro na criação do piso**
   - Causa: Tipo de piso não encontrado
   - Tratamento: Rollback da transação

3. **Erro no carregamento da família**
   - Causa: Arquivo inválido ou corrompido
   - Tratamento: Rollback da transação

4. **Cancelamento pelo usuário**
   - Causa: Usuário cancela seleção de arquivo
   - Tratamento: Retorna Result.Cancelled

### Padrão de Transações

```csharp
using (Transaction trans = new Transaction(doc, "Nome da Operação"))
{
    trans.Start();
    try
    {
        // Operações do Revit
        trans.Commit();
    }
    catch (Exception)
    {
        trans.RollBack();
        throw;
    }
}
```

## Configuração do Projeto

### Dependências
- **RevitAPI.dll**: API principal do Revit
- **RevitAPIUI.dll**: Interface do usuário
- **System.Windows.Forms**: Seletor de arquivo
- **.NET Framework 4.8**: Framework base

### Configurações de Build
- **Target Framework**: net48
- **Platform Target**: x64 (compatível com Revit)
- **Copy Local**: False para DLLs do Revit

## Extensibilidade

### Adicionando Novos Comandos

1. Criar nova classe que implementa `IExternalCommand`
2. Adicionar botão em `App.OnStartup()`
3. Registrar no arquivo .addin se necessário

### Exemplo de Novo Comando
```csharp
[Transaction(TransactionMode.Manual)]
public class NovoComando : IExternalCommand
{
    public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
    {
        // Implementação do comando
        return Result.Succeeded;
    }
}
```

### Adicionando ao Painel
```csharp
PushButtonData novoButtonData = new PushButtonData(
    "NovoComandoButton",
    "Novo Comando",
    assemblyPath,
    "BimexDeveloperPlugin.NovoComando");

ribbonPanel.AddItem(novoButtonData);
```

## Performance

### Otimizações Implementadas
- Transações mínimas necessárias
- Verificações de null antes de operações
- Rollback automático em caso de erro
- Regeneração apenas quando necessário

### Considerações de Memória
- Dispose de objetos quando possível
- Uso de `using` statements para recursos
- Evitar referências circulares

## Debugging

### Logs de Debug
O plugin usa `System.Diagnostics.Debug.WriteLine()` para logging:

```csharp
System.Diagnostics.Debug.WriteLine($"Operação: {detalhes}");
```

### Visualização de Logs
- Use Visual Studio Output Window
- Ou ferramentas como DebugView da Microsoft

### Pontos de Breakpoint Recomendados
1. Início de `Execute()`
2. Antes de cada transação
3. Em blocos catch de exceções

## Versionamento

### Compatibilidade
- **Revit 2023**: Compatível
- **Revit 2024**: Testado e validado
- **Revit 2025**: Compatível (requer teste)

### Atualizações de Versão
1. Atualizar `AssemblyVersion` em AssemblyInfo.cs
2. Testar com versões suportadas do Revit
3. Atualizar documentação se necessário

## Segurança

### Validações Implementadas
- Verificação de arquivos .rfa válidos
- Validação de caminhos de arquivo
- Verificação de permissões de escrita
- Tratamento de exceções de segurança

### Boas Práticas
- Não executar código não confiável
- Validar todas as entradas do usuário
- Usar caminhos absolutos quando possível
