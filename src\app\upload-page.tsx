"use client";

import React, { useState, useEffect } from 'react'; // Added useEffect

// Helper function to convert string to Title Case
function toTitleCase(str: string): string {
  if (!str) return '';
  return str.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

// Predefined lists for AI title generation
const russianCities = ["Moscou", "São Petersburgo", "Novosibirsk", "Ecaterimburgo", "Kazan", "Níjni Novgorod", "Samara", "Omsk", "Cheliabinsk", "Rostov-no-Don"];
const greekIslands = ["Creta", "Rodes", "Santorini", "Corfu", "Mykonos", "Zakynthos", "Kefalonia", "Naxos", "Paros", "Skiathos"];
const descriptiveAdjectives = ["Arc<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>xe<PERSON>", "<PERSON>du<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ósmic<PERSON>", "Efêmero", "<PERSON><PERSON>", "Vibrante", "Minimalista", "Geométrico", "Fluido", "Esculpido"];
const creativeNouns = ["Sussurro", "Eco", "Miragem", "Quasar", "Nebulosa", "Origami", "Fluxo", "Ritmo", "Matiz", "Espectro", "Vértice", "Nexo", "Orbe"];
// Ancient European kings' surnames (Western and Eastern Europe)
const ancientKingsSurnames = ["Magno", "Conquistador", "Piedoso", "Justo", "Sábio", "Terrível", "Corajoso", "Católico", "Santo", "Grande", "Cruel", "Belo", "Forte", "Leão", "Martelo", "Vengador", "Libertador", "Imperador", "Glorioso", "Invencível"];


export default function UploadPage() {  const [title, setTitle] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [image, setImage] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [description, setDescription] = useState('');
  const [room, setRoom] = useState<string[]>([]);
  const [category, setCategory] = useState('');
  const [manufacturer, setManufacturer] = useState('');
  const [minimalRevitVersion, setMinimalRevitVersion] = useState('');
  const [price, setPrice] = useState('');
  const [premium, setPremium] = useState(false);
  const [productLink, setProductLink] = useState('');
  const [keyFeatures, setKeyFeatures] = useState('');
  const [detailedDescription, setDetailedDescription] = useState('');
  const [detailedSpecification, setDetailedSpecification] = useState('');
  const [aiAssistantEnabled, setAiAssistantEnabled] = useState(true);
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false); // New state for loading
  const [aiTitleError, setAiTitleError] = useState<string | null>(null); // New state for AI title error

  const generateAITitle = async (currentImage: File | null) => { // Changed to async
    if (!currentImage) return;

    setIsGeneratingTitle(true);
    setAiTitleError(null);
    let baseTitle = "Objeto"; // Default base title

    try {
      const formData = new FormData();
      formData.append('image', currentImage);

      const response = await fetch('/api/generateTitle', {
        method: 'POST',
        body: formData,
      });      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Falha ao gerar dados com IA.');
      }

      const data = await response.json();
      
      // Set the AI-generated data
      if (data.title) {
        baseTitle = toTitleCase(data.title);
      }
      if (data.description) {
        setDescription(data.description);
      }
      if (data.rooms && Array.isArray(data.rooms)) {
        setRoom(data.rooms);
      }
      if (data.category) {
        setCategory(data.category);
      }    } catch (err: any) {
      console.error("Error generating AI data:", err);
      setAiTitleError(err.message || 'Erro ao contatar o assistente IA.');
      // Keep the default baseTitle or a fallback if API fails
      // For example, could fall back to filename-based title here if desired
      let namePart = currentImage.name.substring(0, currentImage.name.lastIndexOf('.')) || currentImage.name;
      namePart = namePart.replace(/[^\\w\\s]/gi, ' ').replace(/_/g, ' '); // Replace non-alphanumeric (safer regex)
      const words = namePart.split(' ').filter(word => word.length > 2 && isNaN(Number(word)) && !/^\\d+$/.test(word));
      if (words.length > 0) {
        baseTitle = toTitleCase(words.join(' '));
      } else {
        baseTitle = "Objeto Principal";
      }
    } finally {
      setIsGeneratingTitle(false);
    }    // Existing "second name" logic with ancient kings surnames added
    let secondName = "";
    const randomNumber = Math.random();

    if (randomNumber < 0.20) { // 20% Russian City
      secondName = russianCities[Math.floor(Math.random() * russianCities.length)];
    } else if (randomNumber < 0.40) { // 20% Greek Island
      secondName = greekIslands[Math.floor(Math.random() * greekIslands.length)];
    } else if (randomNumber < 0.60) { // 20% Ancient Kings Surnames
      secondName = ancientKingsSurnames[Math.floor(Math.random() * ancientKingsSurnames.length)];
    } else { // 40% Vaguely Reminiscent
      const reminiscentWords = [];
      const wordCount = Math.random() < 0.6 ? 1 : 2; // 60% one word, 40% two words

      if (wordCount === 1) {
        if (Math.random() < 0.5) {
          reminiscentWords.push(descriptiveAdjectives[Math.floor(Math.random() * descriptiveAdjectives.length)]);
        } else {
          reminiscentWords.push(creativeNouns[Math.floor(Math.random() * creativeNouns.length)]);
        }
      } else {
        reminiscentWords.push(descriptiveAdjectives[Math.floor(Math.random() * descriptiveAdjectives.length)]);
        reminiscentWords.push(creativeNouns[Math.floor(Math.random() * creativeNouns.length)]);
      }
      secondName = reminiscentWords.join(" ");
    }
    setTitle(`${baseTitle} ${secondName}`.trim());
  };


  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const newImage = e.target.files[0];
      setImage(newImage);
      setPreview(URL.createObjectURL(newImage));
      if (aiAssistantEnabled) {
        generateAITitle(newImage); // No await needed here, let it run in background
      }
    }
  };

  const handleRoomChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = Array.from(e.target.selectedOptions, option => option.value);
    setRoom(selected);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus(null);    setError(null);
    setAiTitleError(null); // Clear AI error on new submission attempt

    if (aiAssistantEnabled && !title && image) {
      await generateAITitle(image); // Await title generation
      // After awaiting, the 'title' state should be updated.
      // The form will use the new 'title' value.
    }

    if (!file || !image) {
      setError('Arquivo e imagem são obrigatórios.');
      return;
    }    try {
      const formData = new FormData();
      formData.append('title', title);
      formData.append('file', file);
      formData.append('image', image);
      formData.append('description', description);
      room.forEach(r => formData.append('room', r));
      formData.append('category', category);
      formData.append('manufacturer', manufacturer);
      formData.append('minimal_revit_version', minimalRevitVersion);
      formData.append('price', price);
      formData.append('premium', premium ? 'true' : 'false');
      formData.append('product_link', productLink);
      formData.append('key_features', keyFeatures);
      formData.append('detailed_description', detailedDescription);
      formData.append('detailed_specification', detailedSpecification);
      const res = await fetch('/api/families', {
        method: 'POST',
        body: formData,
      });
      const data = await res.json();
      if (!res.ok) {
        setError(data.error || 'Erro desconhecido ao enviar.');      } else {
        setStatus('Upload realizado com sucesso!');
        setTitle('');
        setFile(null);
        setImage(null);
        setPreview(null);
        setDescription('');
        setRoom([]);
        setCategory('');
        setManufacturer('');
        setMinimalRevitVersion('');
        setPrice('');
        setPremium(false);
        setProductLink('');
        setKeyFeatures('');
        setDetailedDescription('');
        setDetailedSpecification('');
      }
    } catch (err: any) {
      setError('Erro ao enviar: ' + (err?.message || 'erro desconhecido'));
    }
  };

  return (
    <div style={{ maxWidth: 400, margin: '40px auto', padding: 24, border: '1px solid #eee', borderRadius: 8 }}>
      <h2>Upload de Arquivo</h2>
      
      <div style={{ marginBottom: 16 }}>
        <label htmlFor="aiAssistantCheckbox" style={{ marginRight: 8 }}>Assistente IA:</label>
        <input
          type="checkbox"
          id="aiAssistantCheckbox"
          checked={aiAssistantEnabled}
          onChange={e => {
            setAiAssistantEnabled(e.target.checked);
            if (e.target.checked && image && !title) { 
              generateAITitle(image);
            }
          }}
        />        <p style={{ fontSize: '0.8em', color: '#666', margin: '4px 0 0 0' }}>
          Se marcado, a IA (Gemini API) tentará gerar título, descrição, ambientes e categoria baseados na imagem.
        </p>
      </div>

      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: 16 }}>
          <label>Imagem:</label>
          <input type="file" accept="image/*" onChange={handleImageChange} required style={{ display: 'block', marginTop: 4 }} />
          {preview && <img src={preview} alt="Preview" style={{ marginTop: 8, maxWidth: '100%' }} />}
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Título:</label>
          <input
            type="text"
            value={title}
            onChange={e => setTitle(e.target.value)}
            required
            style={{ width: '100%', padding: 8, marginTop: 4 }}
            id="titleInput" // Added id for potential direct access if needed
          />
          {isGeneratingTitle && <p style={{ fontSize: '0.8em', color: 'blue', margin: '4px 0 0 0' }}>Gerando dados com IA...</p>}
          {aiTitleError && <p style={{ fontSize: '0.8em', color: 'red', margin: '4px 0 0 0' }}>Erro IA: {aiTitleError}</p>}
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Descrição:</label>
          <textarea
            value={description}
            onChange={e => setDescription(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4 }}
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Ambiente (Room):</label>
          <select multiple value={room} onChange={handleRoomChange} style={{ width: '100%', padding: 8, marginTop: 4 }}>
            <option value="Quarto">Quarto</option>
            <option value="Closet">Closet</option>
            <option value="Banheiro">Banheiro</option>
            <option value="Sala de Estar">Sala de Estar</option>
            <option value="Sala de Jantar">Sala de Jantar</option>
            <option value="Cozinha">Cozinha</option>
            <option value="Despensa">Despensa</option>
            <option value="Área de serviço">Área de serviço</option>
            <option value="Área de lazer">Área de lazer</option>
            <option value="Jardim">Jardim</option>
            <option value="Corredor">Corredor</option>
            <option value="Sacada">Sacada</option>
            <option value="Escritorio">Escritorio</option>
          </select>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Categoria:</label>
          <select value={category} onChange={e => setCategory(e.target.value)} required style={{ width: '100%', padding: 8, marginTop: 4 }}>
            <option value="">Selecione</option>
            <option value="arquitetonico">Arquitetônico</option>
            <option value="estrutural">Estrutural</option>
            <option value="hidrossanitario">Hidrossanitário</option>
            <option value="eletrico">Elétrico</option>
            <option value="iluminacao">Iluminação</option>
            <option value="design_de_interiores">Design de Interiores</option>
            <option value="protecao_contra_incendio">Proteção contra Incêndio</option>
            <option value="protecao_contra_descargas_atmosfericas">Proteção contra Descargas Atmosféricas</option>
            <option value="sinalizacao">Sinalização</option>
            <option value="automacao">Automação</option>
            <option value="paisagismo">Paisagismo</option>
            <option value="drenagem_pluvial">Drenagem Pluvial</option>
            <option value="acessibilidade">Acessibilidade</option>
            <option value="seguranca_no_trabalho">Segurança no Trabalho</option>
          </select>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Fabricante:</label>
          <select value={manufacturer} onChange={e => setManufacturer(e.target.value)} required style={{ width: '100%', padding: 8, marginTop: 4 }}>
            <option value="">Selecione</option>
            <option value="Bimex">Bimex</option>
            <option value="Outros">Outros</option>
          </select>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Versão mínima do Revit:</label>
          <input
            type="text"
            value={minimalRevitVersion}
            onChange={e => setMinimalRevitVersion(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4 }}
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Preço:</label>
          <input
            type="number"
            value={price}
            onChange={e => setPrice(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4 }}
            min="0"
            step="0.01"
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Premium:</label>
          <input
            type="checkbox"
            checked={premium}
            onChange={e => setPremium(e.target.checked)}
            style={{ marginLeft: 8 }}
          />
        </div>        <div style={{ marginBottom: 16 }}>
          <label>Link do Produto:</label>
          <input
            type="text"
            value={productLink}
            onChange={e => setProductLink(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4 }}
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Características Principais (Key Features) - HTML:</label>
          <textarea
            value={keyFeatures}
            onChange={e => setKeyFeatures(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4, minHeight: 80 }}
            placeholder="Exemplo: <ul><li>Resistente à água</li><li>Fácil instalação</li></ul>"
          />
          <p style={{ fontSize: '0.8em', color: '#666', margin: '4px 0 0 0' }}>
            Este campo aceita formatação HTML para destacar características importantes do produto.
          </p>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Descrição Detalhada:</label>
          <textarea
            value={detailedDescription}
            onChange={e => setDetailedDescription(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4, minHeight: 120 }}
            placeholder="Descrição completa e detalhada do produto..."
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Especificação Detalhada:</label>
          <textarea
            value={detailedSpecification}
            onChange={e => setDetailedSpecification(e.target.value)}
            style={{ width: '100%', padding: 8, marginTop: 4, minHeight: 120 }}
            placeholder="Especificações técnicas detalhadas, dimensões, materiais, etc..."
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>Arquivo:</label>
          <input type="file" onChange={handleFileChange} required style={{ display: 'block', marginTop: 4 }} />
        </div>
        <button type="submit" style={{ padding: '8px 16px', background: '#0070f3', color: '#fff', border: 'none', borderRadius: 4 }}>
          Enviar
        </button>
      </form>
      {status && <div style={{ color: 'green', marginTop: 16 }}>{status}</div>}
      {error && <div style={{ color: 'red', marginTop: 16 }}>{error}</div>}
    </div>
  );
}
