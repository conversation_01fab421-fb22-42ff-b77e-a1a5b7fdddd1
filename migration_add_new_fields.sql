-- Migração para adicionar novos campos à tabela families existente
-- Execute este script se você já tem uma tabela families criada

-- Adicionar novos campos se eles não existirem
ALTER TABLE families 
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS room TEXT[],
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS manufacturer TEXT,
ADD COLUMN IF NOT EXISTS minimal_revit_version TEXT,
ADD COLUMN IF NOT EXISTS price DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS premium BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS product_link TEXT,
ADD COLUMN IF NOT EXISTS key_features TEXT,
ADD COLUMN IF NOT EXISTS detailed_description TEXT,
ADD COLUMN IF NOT EXISTS detailed_specification TEXT,
ADD COLUMN IF NOT EXISTS last_modified_at TIMESTAMP;

-- Coment<PERSON>rios sobre os novos campos
COMMENT ON COLUMN families.key_features IS 'Campo HTML para características principais do produto';
COMMENT ON COLUMN families.detailed_description IS 'Campo textual para descrição detalhada do produto';
COMMENT ON COLUMN families.detailed_specification IS 'Campo textual para especificação técnica detalhada';
