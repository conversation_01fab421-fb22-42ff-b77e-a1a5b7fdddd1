using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core; 
using System.Windows.Forms; 
using System.Linq;
using System;
using System.Drawing; // Added for Point and Screen
using System.Net; // Added for WebClient
using System.Collections.Generic; // Added for ISet<>
using System.Text.RegularExpressions;

using Newtonsoft.Json;
using Autodesk.Revit.Exceptions; // Added for OperationCanceledException

namespace RevitPluginWebView
{    // Define classe para payload JSON
    internal class WebMessagePayload
    {
        public string file { get; set; }
        public string title { get; set; }
    }

    // Helper class to wrap Revit's main window handle for Show() method
    public class RevitWindowHandle : System.Windows.Forms.IWin32Window
    {
        private IntPtr _hwnd;
        public RevitWindowHandle(IntPtr hwnd)
        {
            _hwnd = hwnd;
        }
        public IntPtr Handle { get { return _hwnd; } }
    }

    public class LoadFamilyEventHandler : IExternalEventHandler
    {
        private string _fileUrl;
        private string _title;

        public string FileUrl { set => _fileUrl = value; }
        public string Title { set => _title = value; }

        public void Execute(UIApplication app) // UIApplication is passed by Revit when the event is raised
        {
            if (string.IsNullOrEmpty(_fileUrl))
            {
                System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: FileUrl is not set.");
                return;
            }

            UIDocument uidoc = app.ActiveUIDocument; // Get UIDocument from the UIApplication argument
            if (uidoc == null)
            {
                System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: ActiveUIDocument is null.");
                MessageBox.Show("Não há um documento ativo no Revit para carregar a família.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            Document doc = uidoc.Document;

            System.Diagnostics.Debug.WriteLine($"LoadFamilyEventHandler: Executing for File: {_fileUrl}, Title: {_title}");

            try
            {
                // Cria um diretório temporário para baixar o arquivo
                string tempDir = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "BimexFamilies");
                System.IO.Directory.CreateDirectory(tempDir);

                // Gera um nome de arquivo local baseado na URL
                string fileName = System.IO.Path.GetFileName(new Uri(_fileUrl).LocalPath);
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"familia_{DateTime.Now.Ticks}.rfa";
                }
                string localFilePath = System.IO.Path.Combine(tempDir, fileName);
                System.Diagnostics.Debug.WriteLine($"DownloadAndLoadFamily: Caminho local do arquivo: {localFilePath}");

                // Baixa o arquivo
                using (WebClient client = new WebClient())
                {
                    System.Diagnostics.Debug.WriteLine($"DownloadAndLoadFamily: Baixando de {_fileUrl}...");
                    client.DownloadFile(_fileUrl, localFilePath);
                    System.Diagnostics.Debug.WriteLine("DownloadAndLoadFamily: Download concluído.");
                }

                System.Diagnostics.Debug.WriteLine("DownloadAndLoadFamily: Preparando TaskDialog...");
                TaskDialog taskDialog = new TaskDialog("Carregar Família BIMEX");
                taskDialog.MainInstruction = $"Família '{_title}' pronta para carregar.";
                taskDialog.MainContent = "O arquivo da família foi baixado com sucesso.\n\nClique em OK para carregar a família no projeto atual e posicioná-la.";
                taskDialog.CommonButtons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                taskDialog.DefaultButton = TaskDialogResult.Ok;

                System.Diagnostics.Debug.WriteLine("DownloadAndLoadFamily: Mostrando TaskDialog...");
                TaskDialogResult dialogResult = taskDialog.Show();
                System.Diagnostics.Debug.WriteLine($"LoadFamilyEventHandler: TaskDialog.Show() retornou: {dialogResult}");

                if (dialogResult == TaskDialogResult.Ok)
                {
                    System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: Usuário clicou em OK. Prosseguindo para carregar e posicionar a família.");
                    using (Transaction trans = new Transaction(doc, "Carregar Família e Iniciar Posicionamento"))
                    {
                        trans.Start();
                        Family loadedFamily = null;
                        bool loadResult = doc.LoadFamily(localFilePath, out loadedFamily);

                        if (loadResult && loadedFamily != null)
                        {
                            FamilySymbol familySymbol = null;
                            ISet<ElementId> familySymbolIds = loadedFamily.GetFamilySymbolIds();
                            if (familySymbolIds.Count > 0)
                            {
                                familySymbol = doc.GetElement(familySymbolIds.First()) as FamilySymbol;
                            }

                            if (familySymbol != null)
                            {
                                if (!familySymbol.IsActive)
                                {
                                    familySymbol.Activate();
                                    doc.Regenerate(); 
                                }
                                trans.Commit(); 
                                try
                                {
                                    uidoc.PromptForFamilyInstancePlacement(familySymbol);
                                }
                                catch (Autodesk.Revit.Exceptions.OperationCanceledException)
                                {
                                    // User cancelled the placement, this is an expected behavior.
                                    System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: User cancelled family placement.");
                                    // Optionally, inform the user via a less intrusive method or simply do nothing.
                                }
                                // Other exceptions during placement will be caught by the outer catch block.
                            }
                            else
                            {
                                trans.RollBack();
                                MessageBox.Show("Não foi possível encontrar um tipo carregável para a família.", "Erro ao Carregar", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            trans.RollBack();
                            MessageBox.Show("Não foi possível carregar a família. Verifique se o arquivo é uma família Revit válida (.rfa) e se não está corrompido.", "Erro ao Carregar", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                else
                {
                     System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: User cancelled the operation.");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LoadFamilyEventHandler: ERRO - {ex.ToString()}");
                // Check if the exception is due to user cancellation to avoid showing the generic error message for that.
                if (ex is Autodesk.Revit.Exceptions.OperationCanceledException)
                {
                    // This specific check might be redundant if the inner try-catch for PromptForFamilyInstancePlacement handles it,
                    // but it's a safeguard.
                    System.Diagnostics.Debug.WriteLine("LoadFamilyEventHandler: OperationCanceledException caught in outer handler (should have been caught by inner).");
                }
                else
                {
                    MessageBox.Show($"Erro ao baixar ou carregar família (EventHandler): {ex.Message}", "Erro Crítico no Handler", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        public string GetName()
        {
            return "BIMEX Load Family Event Handler";
        }
    }

    [Transaction(TransactionMode.Manual)]
    public class OpenWebViewCommand : IExternalCommand
    {
        private ExternalCommandData _commandData; // Still useful for initial RevitWindowHandle if needed, but not for the handler's core logic
        private LoadFamilyEventHandler _loadFamilyHandler;
        private ExternalEvent _externalLoadFamilyEvent;

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            _commandData = commandData;
            
            _loadFamilyHandler = new LoadFamilyEventHandler();
            _externalLoadFamilyEvent = ExternalEvent.Create(_loadFamilyHandler);

            // Definir URL do site (usando http://localhost:3000 para desenvolvimento)
            string url = "http://localhost:3000/families-plugin";
            System.Diagnostics.Debug.WriteLine($"Iniciando WebView com URL: {url}");

            // Cria um formulário para hospedar o WebView2
            System.Windows.Forms.Form webViewForm = new System.Windows.Forms.Form();
            webViewForm.Text = "Catálogo de Famílias BIMEX (WebView2)";            webViewForm.Width = 500;
            webViewForm.Height = System.Windows.Forms.Screen.PrimaryScreen.Bounds.Height; // Set to full screen height, qualified Screen
            webViewForm.StartPosition = FormStartPosition.Manual; // Allow manual positioning
            webViewForm.Location = new System.Drawing.Point(System.Windows.Forms.Screen.PrimaryScreen.Bounds.Width - webViewForm.Width, 0); // Position at top-right, qualified Point and Screen
            
            // Inicializa o WebView2
            WebView2 webView = new WebView2();
            webView.Dock = DockStyle.Fill;
            
            // É importante inicializar o CoreWebView2 antes de navegar
            // Isso pode ser feito de forma assíncrona
            webViewForm.Controls.Add(webView);
            
            // Configurar o handler para interceptar navegação de URLs com esquema personalizado "revit://"
            webViewForm.Load += async (sender, e) =>
            {
                string userDataFolder = null; // Declare here to be in scope for catch
                try
                {
                    System.Diagnostics.Debug.WriteLine("Iniciando carregamento do WebView2...");
                    
                    // Define a pasta de dados do usuário em um local com permissões de escrita (pasta temporária do sistema)
                    userDataFolder = System.IO.Path.Combine(System.IO.Path.GetTempPath(), "BimexPlugin_WebView2_UserData");
                    
                    // Garante que o diretório exista
                    System.IO.Directory.CreateDirectory(userDataFolder);
                    System.Diagnostics.Debug.WriteLine($"Diretório de dados do usuário: {userDataFolder}");

                    // Criando environment com opções padrão
                    CoreWebView2EnvironmentOptions options = new CoreWebView2EnvironmentOptions(); 
                    CoreWebView2Environment environment = await CoreWebView2Environment.CreateAsync(
                        browserExecutableFolder: null, 
                        userDataFolder: userDataFolder, 
                        options: options);
                    
                    System.Diagnostics.Debug.WriteLine("Ambiente WebView2 criado, inicializando WebView2...");
                    
                    // Inicializar o componente WebView2
                    await webView.EnsureCoreWebView2Async(environment);
                    System.Diagnostics.Debug.WriteLine("WebView2 inicializado com sucesso!");if (webView.CoreWebView2 != null)
                    {
                        // Configuração do WebView2
                        
                        // 1. Adicionar handler para o protocolo personalizado (antes de navegar)
                        // webView.CoreWebView2.NavigationStarting += CoreWebView2_NavigationStarting;
                        // NavigationStarting handler removed in favor of WebMessageReceived communication

                        // 2. Habilitar DevTools para depuração
                        webView.CoreWebView2.Settings.AreDevToolsEnabled = true;
                        
                        // 3. Opcional: abre DevTools automaticamente para depuração
                        // webView.CoreWebView2.OpenDevToolsWindow();
                        
                        // 4. Permitir redirecionamentos para protocolos personalizados
                        webView.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = true;
                        
                        // 5. Navegar para a URL inicial
                        webView.CoreWebView2.Navigate(url);
                        
                        // 6. Registrar log de navegação
                        webView.CoreWebView2.NavigationCompleted += (s, ev) => {
                            if (ev.IsSuccess)
                            {
                                System.Diagnostics.Debug.WriteLine($"Navegação concluída com sucesso: {webView.CoreWebView2.Source}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"Erro na navegação: {ev.WebErrorStatus}");
                            }
                        };
                        
                        // 7. Registrar handler para mensagens da Web
                        webView.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                    }
                    else
                    {
                        MessageBox.Show("Não foi possível inicializar o CoreWebView2. Verifique se o WebView2 Runtime está instalado.", "Erro WebView2", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (System.Exception ex)
                {
                    string errorMessage = $"Erro ao inicializar WebView2: {ex.Message}\n\nStack Trace: {ex.StackTrace}\n\nVerifique se o Microsoft Edge WebView2 Runtime está instalado em seu sistema e se todas as DLLs do WebView2 (Core, WinForms, WebView2Loader.dll) estão na pasta do Addin.";
                    if (!string.IsNullOrEmpty(userDataFolder))
                    {
                        errorMessage += $"\n\nUserDataFolder Tentado: {userDataFolder}";
                    }
                    MessageBox.Show(errorMessage, "Erro de Inicialização", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };
              // Evento para tratar erros de navegação
            
            // Exibe o formulário como não-modal
            webViewForm.Show(new RevitWindowHandle(commandData.Application.MainWindowHandle)); // Show as non-modal, owned by Revit
            System.Diagnostics.Debug.WriteLine("Formulário exibido.");

            return Result.Succeeded;
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                string jsonMessage = e.WebMessageAsJson; // Prioritize WebMessageAsJson
                System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: WebMessageAsJson: {jsonMessage}");

                if (string.IsNullOrEmpty(jsonMessage))
                {
                    System.Diagnostics.Debug.WriteLine("CoreWebView2_WebMessageReceived: WebMessageAsJson was null or empty. Falling back to TryGetWebMessageAsString.");
                    jsonMessage = e.TryGetWebMessageAsString();
                    System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: TryGetWebMessageAsString: {jsonMessage}");
                }

                if (string.IsNullOrEmpty(jsonMessage))
                {
                    System.Diagnostics.Debug.WriteLine("CoreWebView2_WebMessageReceived: Message content is still null or empty after both attempts. Aborting.");
                    return;
                }

                WebMessagePayload payload = JsonConvert.DeserializeObject<WebMessagePayload>(jsonMessage);
                
                if (payload != null)
                {
                    System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: Payload deserialized - File: {payload.file}, Title: {payload.title}");
                    if (!string.IsNullOrEmpty(payload.file))
                    {
                        System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: Payload for event - File: {payload.file}, Title: {payload.title}");
                        _loadFamilyHandler.FileUrl = payload.file;
                        _loadFamilyHandler.Title = payload.title ?? "Família BIM (Título Padrão)";
                        _externalLoadFamilyEvent.Raise();
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("CoreWebView2_WebMessageReceived: payload.file is empty or null.");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("CoreWebView2_WebMessageReceived: Failed to deserialize JSON payload.");
                }
            }
            catch (JsonException jsonEx)
            {
                System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: JSON deserialization error: {jsonEx.Message}");
            }
            catch (System.ArgumentException argEx) when (argEx.Message.Contains("Class not registered")) // Explicitly use System.ArgumentException
            {
                System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: WebView2 Class not registered error: {argEx.ToString()}");
                MessageBox.Show("Erro de WebView2: Classe não registrada. Pode ser necessário reinstalar ou atualizar o WebView2 Runtime.", "Erro WebView2", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CoreWebView2_WebMessageReceived: Error processing web message: {ex.ToString()}");
            }
        }
        // ... (CoreWebView2_NavigationStarting if it exists and is used)
    }

    // Classe da Aplicação para criar o botão na Ribbon do Revit
    public class App : IExternalApplication
    {
        public Result OnStartup(UIControlledApplication application)
        {
            string panelName = "BIMEX";
            string buttonText = "BIMEX";

            RibbonPanel ribbonPanel = null;
            // Tenta obter os painéis existentes na aba Complementos
            System.Collections.Generic.List<RibbonPanel> existingPanels = application.GetRibbonPanels(Tab.AddIns);
            ribbonPanel = existingPanels.FirstOrDefault(p => p.Name == panelName);

            // Se o painel não existir, cria um novo
            if (ribbonPanel == null)
            {
                ribbonPanel = application.CreateRibbonPanel(Tab.AddIns, panelName);
            }

            // Informações do assembly para localizar o comando
            string assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;

            // Cria o botão para abrir o WebView
            PushButtonData buttonData = new PushButtonData(
                "BimexPlugin_OpenCatalogButton", // Nome interno único para o botão
                buttonText,                      // Texto que aparece no botão
                assemblyPath,
                "RevitPluginWebView.OpenWebViewCommand"); // Namespace.ClassName do comando

            // Adiciona o botão ao painel
            // Verifica se um botão com o mesmo nome já existe para evitar duplicatas (opcional, mas bom para robustez)
            if (ribbonPanel.GetItems().FirstOrDefault(item => item.Name == "BimexPlugin_OpenCatalogButton") == null)
            {
                 PushButton pushButton = ribbonPanel.AddItem(buttonData) as PushButton;
                // Opcional: Adicionar um ícone ao botão (ex: .png 32x32)
                // pushButton.LargeImage = new System.Windows.Media.Imaging.BitmapImage(new System.Uri("caminho/para/seu/icone.png"));
            }

            return Result.Succeeded;
        }

        public Result OnShutdown(UIControlledApplication application)
        { 
            return Result.Succeeded;
        }
    }
}
