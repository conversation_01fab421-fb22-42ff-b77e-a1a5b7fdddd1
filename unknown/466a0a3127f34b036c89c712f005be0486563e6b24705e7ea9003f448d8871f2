{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\old\\Documentos\\bimex-object-market\\bimex-object-market\\plugin-revit\\RevitPluginWebView.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\old\\Documentos\\bimex-object-market\\bimex-object-market\\plugin-revit\\RevitPluginWebView.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\old\\Documentos\\bimex-object-market\\bimex-object-market\\plugin-revit\\RevitPluginWebView.csproj", "projectName": "OpenWebViewCommand", "projectPath": "C:\\Users\\<USER>\\OneDrive\\old\\Documentos\\bimex-object-market\\bimex-object-market\\plugin-revit\\RevitPluginWebView.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\old\\Documentos\\bimex-object-market\\bimex-object-market\\plugin-revit\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.1264.42, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}