<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework> <!-- Ajuste se usar uma versão diferente do .NET Framework para o Revit -->
    <RootNamespace>RevitPluginWebView</RootNamespace>
    <AssemblyName>OpenWebViewCommand</AssemblyName>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems> <!-- Desabilitamos itens padrão para adicionar manualmente -->
	<UseWindowsForms>true</UseWindowsForms>
    <PlatformTarget>x64</PlatformTarget> <!-- Adicionado para corresponder à arquitetura do Revit -->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies> <!-- Ensure dependencies are copied -->
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="OpenWebViewCommand.cs" />
  </ItemGroup>

  <ItemGroup>
    <!-- 
      IMPORTANTE: Ajuste os caminhos abaixo para apontar para os arquivos RevitAPI.dll e RevitAPIUI.dll 
      na pasta de instalação do Revit no seu computador.
      Exemplo de caminho comum: C:\Program Files\Autodesk\Revit 202X\
    -->
    <Reference Include="RevitAPI">
      <HintPath>C:\\Program Files\\Autodesk\\Revit 2024\\RevitAPI.dll</HintPath> <!-- Exemplo para Revit 2023 -->
      <Private>False</Private>
    </Reference>
    <Reference Include="RevitAPIUI">
      <HintPath>C:\\Program Files\\Autodesk\\Revit 2024\\RevitAPIUI.dll</HintPath> <!-- Exemplo para Revit 2023 -->
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1264.42" /> <!-- Ou a versão mais recente estável -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
