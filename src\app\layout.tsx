import type React from "react";
import type { Metadata } from "next";
import { Playfair_Display, Inter, Cormoran<PERSON>_Garamond } from "next/font/google";
import "./globals.css";

const playfairDisplay = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
  display: "swap",
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter", // Ensure this matches the variable in globals.css and tailwind.config.ts
  display: "swap",
});

const cormorantGaramond = Cormorant_Garamond({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-cormorant",
  display: "swap",
});

export const metadata: Metadata = {
  title: "BIMEX - Premium Revit Families",
  description:
    "Discover our curated collection of high-quality Revit families, meticulously crafted for architectural excellence.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${playfairDisplay.variable} ${inter.variable} ${cormorantGaramond.variable}`}>
      <body className="font-inter antialiased">{children}</body>
    </html>
  )
}
