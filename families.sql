-- Criação do banco de dados families e tabela families com id sequencial
CREATE TABLE families (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    file_path TEXT NOT NULL,
    image_path TEXT NOT NULL,
    description TEXT,
    room TEXT[],
    category TEXT,
    tags TEXT[],
    manufacturer TEXT,
    minimal_revit_version TEXT,
    price DECIMAL(10,2),
    premium BOOLEAN DEFAULT FALSE,
    product_link TEXT,
    key_features TEXT, -- Campo HTML para características principais
    detailed_description TEXT, -- Campo textual para descrição detalhada
    detailed_specification TEXT, -- Campo textual para especificação detalhada
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP
);
