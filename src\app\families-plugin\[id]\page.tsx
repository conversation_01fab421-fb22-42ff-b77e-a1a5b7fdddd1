"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Download, Heart, Share2, Star, ChevronLeft, ChevronRight, Maximize2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface FamilyData {
  id: number
  title: string
  file_url: string | null
  image_url: string | null
  created_at: string
  description?: string
  room?: string[]
  category?: string
  tags?: string[]
  manufacturer?: string
  minimal_revit_version?: string
  price?: number
  premium?: boolean
  product_link?: string
  key_features?: string
  detailed_description?: string
  detailed_specification?: string
  // Keep mock fields for UI compatibility
  subcategory?: string
  rating?: number
  reviewCount?: number
  downloads?: number
  longDescription?: string
  fileSize?: string
  compatibility?: string[]
  ambientes?: string[]
  technicalInfo?: {
    lodLevel?: string
    parametric?: boolean
  }
  specifications?: {
    features?: string[]
    dimensions?: Record<string, string>
    materials?: Record<string, string>
  }
}

// Process real database data with fallbacks for UI compatibility
const processRealData = (family: any): FamilyData => {
  // Parse key_features from HTML/text to array
  let features: string[] = []
  if (family.key_features) {
    try {
      // Try to parse as JSON array first
      features = JSON.parse(family.key_features)
    } catch {
      // If not JSON, split by line breaks or bullet points
      features = family.key_features
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .split(/\n|•|-|\*/)
        .map((item: string) => item.trim())
        .filter((item: string) => item.length > 0)
    }
  }

  // Parse detailed_specification for dimensions and materials
  let dimensions: Record<string, string> = {}
  let materials: Record<string, string> = {}
  
  if (family.detailed_specification) {
    // Simple parsing - you can make this more sophisticated based on your data format
    const lines = family.detailed_specification.split('\n')
    lines.forEach((line: string) => {
      if (line.toLowerCase().includes('dimensão') || line.toLowerCase().includes('medida')) {
        const match = line.match(/(\w+):\s*([^,\n]+)/)
        if (match) dimensions[match[1]] = match[2]
      }
      if (line.toLowerCase().includes('material')) {
        const match = line.match(/(\w+):\s*([^,\n]+)/)
        if (match) materials[match[1]] = match[2]
      }
    })
  }

  return {
    ...family,
    // Use real database fields
    description: family.description || "Descrição não disponível",
    longDescription: family.detailed_description || family.description || "Descrição detalhada não disponível",
    ambientes: family.room || [],
    
    // Mock data for fields not in database (for UI compatibility)
    subcategory: family.subcategory || "Mobiliário", 
    rating: family.rating || 4.8,
    reviewCount: family.reviewCount || 42,
    downloads: family.downloads || 1250,
    fileSize: family.fileSize || "2.4 MB",
    compatibility: family.compatibility || ["Revit 2020", "Revit 2021", "Revit 2022", "Revit 2023", "Revit 2024"],
    technicalInfo: {
      lodLevel: "LOD 300",
      parametric: true,
      ...family.technicalInfo
    },
    specifications: {
      features: features.length > 0 ? features : [
        "Design parametrizado com dimensões ajustáveis",
        "Definições de materiais de alta qualidade",
        "Otimizado para performance",
        "Componentes padrão da indústria",
        "Documentação profissional"
      ],
      dimensions: Object.keys(dimensions).length > 0 ? dimensions : {
        largura: "650mm",
        profundidade: "650mm", 
        altura: "1120mm",
        alturaAssento: "450-550mm"
      },
      materials: Object.keys(materials).length > 0 ? materials : {
        estrutura: "Alumínio",
        estofamento: "Couro Premium",
        base: "Cromo Polido"
      },
      ...family.specifications
    }
  }
}

// Mock related families
const relatedFamilies = [
  {
    id: 2,
    name: "Executive Desk",
    image: "/placeholder.svg?height=200&width=200",
    category: "Furniture",
    rating: 4.8,
  },
  {
    id: 3,
    name: "Office Bookshelf", 
    image: "/placeholder.svg?height=200&width=200",
    category: "Furniture",
    rating: 4.7,
  },
  {
    id: 4,
    name: "Meeting Table",
    image: "/placeholder.svg?height=200&width=200", 
    category: "Furniture",
    rating: 4.9,
  },
]

// Mock reviews
const reviews = [
  {
    id: 1,
    author: "Sarah Chen",
    role: "Senior Architect",
    company: "Foster + Partners",
    rating: 5,
    date: "2 weeks ago",
    comment: "Exceptional quality and attention to detail. The parametric controls are intuitive and the model renders beautifully in our presentations.",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 2,
    author: "Michael Rodriguez", 
    role: "BIM Manager",
    company: "Gensler",
    rating: 5,
    date: "1 month ago",
    comment: "Perfect for our high-end office projects. The level of detail is outstanding and it integrates seamlessly with our Revit workflows.",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 3,
    author: "Emma Thompson",
    role: "Interior Designer",
    company: "HOK", 
    rating: 4,
    date: "2 months ago",
    comment: "Great model with excellent material definitions. Would love to see more color variations in future updates.",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

export default function FamilyDetailPage() {
  const params = useParams()
  const id = params?.id as string
  const [family, setFamily] = useState<FamilyData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isLiked, setIsLiked] = useState(false)

  useEffect(() => {
    if (id) {
      async function fetchFamilyDetail() {
        try {
          setLoading(true)
          const response = await fetch(`/api/families/${id}`)
          const contentType = response.headers.get('content-type')
          if (!response.ok) {
            let errorData = { message: '' }
            if (contentType && contentType.includes('application/json')) {
              errorData = await response.json()
            } else {
              errorData.message = await response.text()
            }
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
          }
          let data = null
          if (contentType && contentType.includes('application/json')) {
            data = await response.json()
          } else {
            throw new Error('Resposta da API não é JSON.')
          }
          setFamily(processRealData(data))
          setError(null)
        } catch (e: any) {
          console.error("Fetch error:", e)
          setError(e.message || "Failed to load family details.")
        } finally {
          setLoading(false)
        }
      }
      fetchFamilyDetail()
    }
  }, [id])

  const handleLoadInRevit = () => {
    if (family && family.file_url) {
      // Access WebView2 host via window.chrome.webview
      const webview = (window as any).chrome?.webview
      if (webview && typeof webview.postMessage === 'function') {
        webview.postMessage({ file: family.file_url, title: family.title })
        alert('Comando enviado para o Revit. Posicione a família no modelo.')
      } else {
        alert('Integração com o Revit não disponível.')
      }
    } else {
      alert('URL do arquivo não disponível para carregamento.')
    }
  }

  const nextImage = () => {
    if (family && family.image_url) {
      setCurrentImageIndex((prev) => (prev + 1) % 1) // Only one image for now
    }
  }

  const prevImage = () => {
    if (family && family.image_url) {
      setCurrentImageIndex((prev) => (prev - 1 + 1) % 1) // Only one image for now
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <p className="text-lg">Loading family details...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-5 text-red-600">
        <h1 className="text-xl font-bold">Error Loading Family Details</h1>
        <p>{error}</p>
        <Link href="/families-plugin" className="text-blue-600 underline">
          ← Back to catalog
        </Link>
      </div>
    )
  }

  if (!family) {
    return (
      <div className="p-5">
        <p>Family not found.</p>
        <Link href="/families-plugin" className="text-blue-600 underline">
          ← Back to catalog
        </Link>
      </div>
    )
  }

  // Mock additional images array (using the same image for demo)
  const images = family.image_url ? [family.image_url] : ["/placeholder.svg"]

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-12">
              <Link href="/families-plugin">
                <h1 className="text-4xl font-display font-bold tracking-luxury text-black cursor-pointer">BIMEX</h1>
              </Link>
              <nav className="hidden md:flex space-x-10">                <Link
                  href="/families-plugin"
                  className="text-caption text-gray-900 hover:text-gray-600 transition-colors tracking-premium"
                >
                  Catálogo
                </Link>                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors tracking-premium"
                >
                  Coleções
                </a>
                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors tracking-premium"
                >
                  Sobre
                </a>
                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors tracking-premium"
                >
                  Suporte
                </a>
              </nav>
            </div>
            <div className="flex items-center space-x-4">              <Button variant="outline" size="sm" className="text-caption tracking-premium">
                Entrar
              </Button>
              <Button size="sm" className="bg-black text-white hover:bg-gray-800 text-caption tracking-premium">
                Assinar
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <section className="border-b border-gray-100 bg-gray-50">
        <div className="container mx-auto px-4 py-4">          <div className="flex items-center space-x-2 text-body-sm text-gray-600">
            <Link href="/families-plugin" className="hover:text-gray-900 transition-colors font-serif">
              Catálogo
            </Link>
            <span>/</span>
            <span className="font-serif">{family.category?.replace(/_/g, ' ')}</span>
            <span>/</span>
            <span className="text-gray-900 font-serif">{family.title}</span>
          </div>
        </div>
      </section>

      {/* Back Button */}
      <section className="py-6">
        <div className="container mx-auto px-4">          <Link href="/families-plugin">
            <Button variant="ghost" className="text-caption tracking-premium hover:bg-gray-50">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar ao Catálogo
            </Button>
          </Link>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">            {/* Image Gallery */}
            <div className="space-y-6">
              <div className="relative group">
                <div className="aspect-square overflow-hidden rounded-lg bg-white">
                  <Image
                    src={images[currentImageIndex] || "/placeholder.svg"}
                    alt={family.title}
                    width={600}
                    height={600}
                    className="w-full h-full object-contain"
                  />
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity backdrop-blur-sm"
                  onClick={() => {}}
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
                {images.length > 1 && (
                  <>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity backdrop-blur-sm"
                      onClick={prevImage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity backdrop-blur-sm"
                      onClick={nextImage}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>

              {/* Thumbnail Gallery */}
              {images.length > 1 && (
                <div className="grid grid-cols-5 gap-3">
                  {images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`aspect-square rounded-md overflow-hidden border-2 transition-all ${
                        index === currentImageIndex ? "border-black" : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`${family.title} view ${index + 1}`}
                        width={120}
                        height={120}
                        className="w-full h-full object-contain"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="space-y-8">
              {/* Header */}              <div>                <div className="flex items-center gap-3 mb-4">
                  <Badge variant="secondary" className="text-caption tracking-premium">
                    {family.category?.replace(/_/g, ' ')}
                  </Badge>
                  <Badge variant="outline" className="text-caption tracking-premium">
                    {family.manufacturer}
                  </Badge>
                </div>
                <h1 className="text-display-md text-black mb-4">{family.title}</h1>
                <div className="flex items-center gap-6 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(family.rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-body-sm font-serif">{family.rating}</span>
                    <span className="text-body-sm text-gray-500 font-serif">({family.reviewCount} reviews)</span>
                  </div>
                  <Separator orientation="vertical" className="h-4" />
                  <span className="text-body-sm text-gray-500 font-serif">
                    {family.downloads?.toLocaleString()} downloads
                  </span>
                </div>
                <p className="text-body-lg text-gray-700 font-serif leading-relaxed">{family.description}</p>
              </div>              {/* Actions */}
              <div className="flex gap-4">                <Button
                  onClick={handleLoadInRevit}
                  size="lg"
                  className="flex-1 bg-black text-white hover:bg-gray-800 text-caption tracking-premium"
                  style={{ color: 'white' }}
                >
                  <Download className="h-5 w-5 mr-2 text-white" />
                  Carregar no Revit
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsLiked(!isLiked)}
                  className={`text-caption tracking-premium ${isLiked ? "text-red-500 border-red-500" : ""}`}
                >
                  <Heart className={`h-5 w-5 ${isLiked ? "fill-red-500" : ""}`} />
                </Button>
                <Button variant="outline" size="lg" className="text-caption tracking-premium">
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>              {/* Quick Info */}              <Card className="p-6">
                <div className="grid grid-cols-2 gap-6 mb-6">
                  <div>
                    <h4 className="text-caption text-gray-500 tracking-premium mb-2">COMPATIBILIDADE</h4>
                    <p className="text-body-md font-serif">
                      {family.minimal_revit_version ? `${family.minimal_revit_version}+` : 'Não especificado'}
                    </p>
                  </div>                  <div>
                    <h4 className="text-caption text-gray-500 tracking-premium mb-2">FABRICANTE</h4>
                    <p className="text-body-md font-serif">{family.manufacturer}</p>
                  </div>                </div>
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <h4 className="text-caption text-gray-500 tracking-premium mb-2">PARAMÉTRICO</h4>
                    <p className="text-body-md font-serif">{family.technicalInfo?.parametric ? "Sim" : "Não"}</p>
                  </div>
                </div></Card>

              {/* Ambientes */}
              <div>
                <h4 className="text-caption text-gray-500 tracking-premium mb-3">AMBIENTES</h4>
                <div className="flex flex-wrap gap-2">
                  {family.ambientes?.map((ambiente) => (
                    <Badge key={ambiente} variant="outline" className="text-caption tracking-premium">
                      {ambiente}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Tags */}
              <div>
                <h4 className="text-caption text-gray-500 tracking-premium mb-3">TAGS</h4>              <div className="flex flex-wrap gap-2">
                  {family.tags?.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-caption tracking-premium">
                      {tag.replace(/_/g, ' ')}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Information Tabs */}
          <div className="mt-20">
            <Tabs defaultValue="overview" className="w-full">              <TabsList className="grid w-full grid-cols-3 mb-8">
                <TabsTrigger value="overview" className="text-caption tracking-premium">
                  Visão Geral
                </TabsTrigger>
                <TabsTrigger value="specifications" className="text-caption tracking-premium">
                  Especificações
                </TabsTrigger>
                <TabsTrigger value="reviews" className="text-caption tracking-premium">
                  Avaliações
                </TabsTrigger>
              </TabsList>              <TabsContent value="overview" className="space-y-8">
                <div>
                  <h3 className="text-heading-lg mb-6">Visão Geral do Produto</h3>
                  <p className="text-body-lg text-gray-700 font-serif leading-relaxed mb-8">
                    {family.longDescription}
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <Card className="p-6">
                      <h4 className="text-heading-sm mb-4">Características Principais</h4>
                      <ul className="space-y-3">
                        {family.specifications?.features?.map((feature, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-1.5 h-1.5 bg-black rounded-full mt-2 flex-shrink-0" />
                            <span className="text-body-md font-serif">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </Card>

                    <Card className="p-6">
                      <h4 className="text-heading-sm mb-4">Compatibilidade</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-body-md font-serif">
                            Revit {family.minimal_revit_version ? `${family.minimal_revit_version}+` : 'Não especificado'}
                          </span>
                          <Badge variant="secondary" className="text-caption">
                            Suportado
                          </Badge>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>
              </TabsContent>              <TabsContent value="specifications" className="space-y-8">
                <div>
                  <h3 className="text-heading-lg mb-6">Especificações Detalhadas</h3>

                  {family.detailed_specification && (
                    <Card className="p-6 mb-8">
                      <h4 className="text-heading-sm mb-4">Especificação Técnica</h4>
                      <div className="text-body-md font-serif leading-relaxed whitespace-pre-line">
                        {family.detailed_specification}
                      </div>
                    </Card>
                  )}                  {family.product_link && (
                    <Card className="p-6">
                      <h4 className="text-heading-sm mb-4">Link</h4>
                      <div className="space-y-3">
                        <a 
                          href={family.product_link} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center px-6 py-3 bg-black text-white hover:bg-gray-800 transition-colors rounded-md text-caption tracking-premium"
                        >
                          Acessar o produto
                        </a>
                      </div>
                    </Card>
                  )}
                </div>
              </TabsContent>              <TabsContent value="reviews" className="space-y-8">
                <div>
                  <div className="flex items-center justify-between mb-8">
                    <h3 className="text-heading-lg">Avaliações dos Clientes</h3>
                    <Button variant="outline" className="text-caption tracking-premium">
                      Escrever Avaliação
                    </Button>
                  </div>

                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <Card key={review.id} className="p-6">
                        <div className="flex items-start gap-4">
                          <Avatar>
                            <AvatarImage src={review.avatar || "/placeholder.svg"} alt={review.author} />
                            <AvatarFallback>
                              {review.author
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="text-heading-sm">{review.author}</h4>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-body-sm text-gray-500 font-serif">{review.date}</span>
                            </div>
                            <p className="text-body-sm text-gray-600 font-serif mb-2">
                              {review.role} at {review.company}
                            </p>
                            <p className="text-body-md font-serif leading-relaxed">{review.comment}</p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Related Families */}          <div className="mt-20">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-heading-lg">Famílias Relacionadas</h3>
              <Link href="/families-plugin">
                <Button variant="outline" className="text-caption tracking-premium">
                  Ver Todas
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedFamilies.map((family) => (
                <Card key={family.id} className="group hover:shadow-lg transition-shadow duration-500">
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden">
                      <Image
                        src={family.image || "/placeholder.svg"}
                        alt={family.name}
                        width={300}
                        height={200}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-700"
                      />
                    </div>
                    <div className="p-6">
                      <h4 className="text-heading-sm mb-2">{family.name}</h4>
                      <div className="flex items-center justify-between">                        <Badge variant="secondary" className="text-caption tracking-premium">
                          {family.category?.replace(/_/g, ' ')}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-body-sm font-serif">{family.rating}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div>
              <h3 className="text-display-sm text-white mb-6">BIMEX</h3>
              <p className="text-body-sm text-gray-400 font-serif leading-relaxed">
                Premium Revit families for architectural excellence and design sophistication.
              </p>
            </div>            <div>
              <h4 className="text-heading-sm text-white mb-6">Produto</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Catálogo
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Coleções
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Novos Lançamentos
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-heading-sm text-white mb-6">Suporte</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Documentação
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Central de Ajuda
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Contato
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-heading-sm text-white mb-6">Empresa</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Sobre
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Carreiras
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Privacidade
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <Separator className="my-12 bg-gray-700" />
          <div className="flex flex-col md:flex-row justify-between items-center">            <p className="text-body-sm text-gray-400 font-serif">© 2024 BIMEX. Todos os direitos reservados.</p>
            <div className="flex space-x-8 mt-6 md:mt-0">
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors tracking-premium">
                Termos
              </a>
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors tracking-premium">
                Privacidade
              </a>
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors tracking-premium">
                Cookies
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
