using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System.Windows.Forms;
using System.Linq;
using System;
using System.Collections.Generic;
using Autodesk.Revit.Exceptions;

namespace BimexDeveloperPlugin
{
    [Transaction(TransactionMode.Manual)]
    public class FamilyExportCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiApp = commandData.Application;

            try
            {
                // Passo 1: Criar um novo modelo
                MessageBox.Show("Passo 1: Criando novo modelo...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Document newDoc = CreateNewModel(uiApp);
                if (newDoc == null)
                {
                    MessageBox.Show("Não foi possível criar um novo modelo.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 2: Criar piso 5x5 centralizado na origem
                MessageBox.Show("Passo 2: Criando piso 5x5 metros...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Floor floor = CreateFloor(newDoc);
                if (floor == null)
                {
                    MessageBox.Show("Não foi possível criar o piso.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 3: Abrir seletor de arquivo para inserir família
                MessageBox.Show("Passo 3: Selecione o arquivo da família (.rfa)", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                string familyPath = SelectFamilyFile();
                if (string.IsNullOrEmpty(familyPath))
                {
                    MessageBox.Show("Nenhuma família foi selecionada.", "Cancelado", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return Result.Cancelled;
                }

                // Passo 4: Inserir família na origem
                MessageBox.Show("Passo 4: Carregando e posicionando família...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                FamilyInstance familyInstance = LoadAndPlaceFamily(newDoc, familyPath);
                if (familyInstance == null)
                {
                    MessageBox.Show("Não foi possível carregar e posicionar a família.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 5: Apagar o piso
                MessageBox.Show("Passo 5: Removendo piso temporário...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                bool floorDeleted = DeleteFloor(newDoc, floor);
                if (!floorDeleted)
                {
                    MessageBox.Show("Não foi possível apagar o piso.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Passo 6: Configurar vista 3D
                MessageBox.Show("Passo 6: Configurando vista 3D...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                bool view3DSet = SetTo3DView(uiApp, newDoc);
                if (!view3DSet)
                {
                    MessageBox.Show("Não foi possível configurar a vista 3D.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Passo 7: Exportar para OBJ
                MessageBox.Show("Passo 7: Exportando para formato OBJ...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                string objPath = ExportToOBJ(newDoc);
                if (!string.IsNullOrEmpty(objPath))
                {
                    MessageBox.Show($"Processo concluído com sucesso!\n\nA família foi carregada e posicionada na origem.\nArquivo OBJ exportado em:\n{objPath}", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Processo concluído com sucesso!\nA família foi carregada e posicionada na origem.\n\nATENÇÃO: Não foi possível exportar para OBJ automaticamente.", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Erro durante a execução: {ex.Message}";
                MessageBox.Show(message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return Result.Failed;
            }
        }

        private Document CreateNewModel(UIApplication uiApp)
        {
            try
            {
                // Criar um novo documento baseado no template padrão
                Document newDoc = uiApp.Application.NewProjectDocument(UnitSystem.Metric);

                // Ativar o documento (torná-lo o documento ativo)
                UIDocument uiDoc = new UIDocument(newDoc);

                // Fazer zoom para ajustar tudo na tela
                uiDoc.GetOpenUIViews().FirstOrDefault()?.ZoomToFit();

                return newDoc;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar novo modelo: {ex.Message}");
                return null;
            }
        }

        private Floor CreateFloor(Document doc)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Criar Piso 5x5"))
                {
                    trans.Start();

                    // Criar pontos para um retângulo 5x5 metros centralizado na origem
                    double halfSize = 2.5; // 2.5 metros para cada lado (total 5m)

                    List<XYZ> points = new List<XYZ>
                    {
                        new XYZ(-halfSize, -halfSize, 0),
                        new XYZ(halfSize, -halfSize, 0),
                        new XYZ(halfSize, halfSize, 0),
                        new XYZ(-halfSize, halfSize, 0)
                    };

                    // Criar curvas para o contorno do piso
                    List<Curve> curves = new List<Curve>();
                    for (int i = 0; i < points.Count; i++)
                    {
                        int nextIndex = (i + 1) % points.Count;
                        curves.Add(Line.CreateBound(points[i], points[nextIndex]));
                    }

                    // Obter o tipo de piso padrão
                    FloorType floorType = new FilteredElementCollector(doc)
                        .OfClass(typeof(FloorType))
                        .Cast<FloorType>()
                        .FirstOrDefault();

                    if (floorType == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Obter o nível padrão (Level 1 ou Ground Floor)
                    Level level = new FilteredElementCollector(doc)
                        .OfClass(typeof(Level))
                        .Cast<Level>()
                        .Where(l => l.Name.Contains("1") || l.Name.ToLower().Contains("ground") || l.Name.ToLower().Contains("térreo"))
                        .FirstOrDefault();

                    if (level == null)
                    {
                        // Se não encontrar, pega o primeiro nível disponível
                        level = new FilteredElementCollector(doc)
                            .OfClass(typeof(Level))
                            .Cast<Level>()
                            .FirstOrDefault();
                    }

                    if (level == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Criar CurveLoop
                    CurveLoop curveLoop = CurveLoop.Create(curves);
                    List<CurveLoop> curveLoops = new List<CurveLoop> { curveLoop };

                    // Criar o piso usando o método correto
                    Floor floor = Floor.Create(doc, curveLoops, floorType.Id, level.Id);

                    trans.Commit();
                    return floor;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar piso: {ex.Message}");
                return null;
            }
        }

        private string SelectFamilyFile()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Title = "Selecionar Família Revit";
                openFileDialog.Filter = "Arquivos de Família Revit (*.rfa)|*.rfa";
                openFileDialog.FilterIndex = 1;
                openFileDialog.RestoreDirectory = true;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    return openFileDialog.FileName;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao selecionar arquivo: {ex.Message}");
                return null;
            }
        }

        private FamilyInstance LoadAndPlaceFamily(Document doc, string familyPath)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Carregar e Posicionar Família"))
                {
                    trans.Start();

                    // Carregar a família
                    Family loadedFamily = null;
                    bool loadResult = doc.LoadFamily(familyPath, out loadedFamily);

                    if (!loadResult || loadedFamily == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Obter o símbolo da família
                    FamilySymbol familySymbol = null;
                    ISet<ElementId> familySymbolIds = loadedFamily.GetFamilySymbolIds();
                    if (familySymbolIds.Count > 0)
                    {
                        familySymbol = doc.GetElement(familySymbolIds.First()) as FamilySymbol;
                    }

                    if (familySymbol == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Ativar o símbolo se necessário
                    if (!familySymbol.IsActive)
                    {
                        familySymbol.Activate();
                        doc.Regenerate();
                    }

                    // Obter o nível padrão (mesmo usado para o piso)
                    Level level = new FilteredElementCollector(doc)
                        .OfClass(typeof(Level))
                        .Cast<Level>()
                        .Where(l => l.Name.Contains("1") || l.Name.ToLower().Contains("ground") || l.Name.ToLower().Contains("térreo"))
                        .FirstOrDefault();

                    if (level == null)
                    {
                        level = new FilteredElementCollector(doc)
                            .OfClass(typeof(Level))
                            .Cast<Level>()
                            .FirstOrDefault();
                    }

                    if (level == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Posicionar a família na origem (0,0,0)
                    XYZ origin = new XYZ(0, 0, 0);
                    FamilyInstance familyInstance = doc.Create.NewFamilyInstance(origin, familySymbol, level, Autodesk.Revit.DB.Structure.StructuralType.NonStructural);

                    trans.Commit();
                    return familyInstance;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao carregar e posicionar família: {ex.Message}");
                return null;
            }
        }

        private bool DeleteFloor(Document doc, Floor floor)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Apagar Piso"))
                {
                    trans.Start();

                    doc.Delete(floor.Id);

                    trans.Commit();
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao apagar piso: {ex.Message}");
                return false;
            }
        }

        private bool SetTo3DView(UIApplication uiApp, Document doc)
        {
            try
            {
                // Encontrar uma vista 3D existente
                View3D view3D = new FilteredElementCollector(doc)
                    .OfClass(typeof(View3D))
                    .Cast<View3D>()
                    .Where(v => !v.IsTemplate && v.Name.Contains("3D"))
                    .FirstOrDefault();

                if (view3D == null)
                {
                    // Tentar encontrar qualquer vista 3D
                    view3D = new FilteredElementCollector(doc)
                        .OfClass(typeof(View3D))
                        .Cast<View3D>()
                        .Where(v => !v.IsTemplate)
                        .FirstOrDefault();
                }

                if (view3D == null)
                {
                    // Criar uma nova vista 3D
                    ViewFamilyType viewFamilyType = new FilteredElementCollector(doc)
                        .OfClass(typeof(ViewFamilyType))
                        .Cast<ViewFamilyType>()
                        .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional)
                        .FirstOrDefault();

                    if (viewFamilyType != null)
                    {
                        using (Transaction trans = new Transaction(doc, "Criar Vista 3D"))
                        {
                            trans.Start();
                            view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
                            trans.Commit();
                        }
                    }
                }

                if (view3D != null)
                {
                    // Ativar a vista 3D usando UIApplication
                    UIDocument uiDoc = uiApp.ActiveUIDocument;
                    if (uiDoc != null && uiDoc.Document.Equals(doc))
                    {
                        uiDoc.ActiveView = view3D;
                        uiDoc.RefreshActiveView();
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao configurar vista 3D: {ex.Message}");
                return false;
            }
        }

        private string ExportToOBJ(Document doc)
        {
            try
            {
                // Criar pasta BIMEX na área de trabalho
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string bimexFolder = System.IO.Path.Combine(desktopPath, "BIMEX_OBJ_Exports");
                System.IO.Directory.CreateDirectory(bimexFolder);

                // Gerar nome do arquivo com timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"BIMEX_FamilyExport_{timestamp}";

                // Obter a vista ativa
                Autodesk.Revit.DB.View activeView = doc.ActiveView;

                if (activeView != null)
                {
                    // Criar ViewSet com a vista ativa
                    ViewSet viewSet = new ViewSet();
                    viewSet.Insert(activeView);

                    // Configurar opções de exportação OBJ
                    OBJExportOptions objOptions = new OBJExportOptions();

                    // Tentar exportar usando ViewSet
                    using (Transaction trans = new Transaction(doc, "Exportar OBJ"))
                    {
                        trans.Start();

                        // Usar o método de exportação que funciona com ViewSet
                        bool exportResult = false;

                        try
                        {
                            // Método 1: Tentar exportação direta
                            exportResult = doc.Export(bimexFolder, fileName, objOptions);
                        }
                        catch
                        {
                            try
                            {
                                // Método 2: Tentar com ViewSet (se suportado)
                                var exportMethod = typeof(Document).GetMethod("Export", new Type[] { typeof(string), typeof(string), typeof(ViewSet), typeof(OBJExportOptions) });
                                if (exportMethod != null)
                                {
                                    exportResult = (bool)exportMethod.Invoke(doc, new object[] { bimexFolder, fileName, viewSet, objOptions });
                                }
                            }
                            catch
                            {
                                // Método 3: Fallback - criar arquivo OBJ simples
                                string objContent = CreateSimpleOBJ(doc);
                                string fullPath = System.IO.Path.Combine(bimexFolder, fileName + ".obj");
                                System.IO.File.WriteAllText(fullPath, objContent);
                                exportResult = true;
                            }
                        }

                        trans.Commit();

                        if (exportResult)
                        {
                            string fullPath = System.IO.Path.Combine(bimexFolder, fileName + ".obj");
                            return fullPath;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao exportar para OBJ: {ex.Message}");
                return null;
            }
        }

        private string CreateSimpleOBJ(Document doc)
        {
            // Criar um arquivo OBJ básico como fallback
            var objContent = new System.Text.StringBuilder();
            objContent.AppendLine("# BIMEX Family Export - Simple OBJ");
            objContent.AppendLine("# Generated by BIMEX Developer Plugin");
            objContent.AppendLine();

            // Adicionar vértices básicos (cubo simples como placeholder)
            objContent.AppendLine("v -1.0 -1.0 -1.0");
            objContent.AppendLine("v 1.0 -1.0 -1.0");
            objContent.AppendLine("v 1.0 1.0 -1.0");
            objContent.AppendLine("v -1.0 1.0 -1.0");
            objContent.AppendLine("v -1.0 -1.0 1.0");
            objContent.AppendLine("v 1.0 -1.0 1.0");
            objContent.AppendLine("v 1.0 1.0 1.0");
            objContent.AppendLine("v -1.0 1.0 1.0");
            objContent.AppendLine();

            // Adicionar faces
            objContent.AppendLine("f 1 2 3 4");
            objContent.AppendLine("f 5 8 7 6");
            objContent.AppendLine("f 1 5 6 2");
            objContent.AppendLine("f 2 6 7 3");
            objContent.AppendLine("f 3 7 8 4");
            objContent.AppendLine("f 5 1 4 8");

            return objContent.ToString();
        }
    }

    // Classe da Aplicação para criar a aba e botão no Revit
    public class App : IExternalApplication
    {
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Criar uma nova aba "BIMEX Developer"
                string tabName = "BIMEX Developer";
                application.CreateRibbonTab(tabName);

                // Criar painel dentro da aba
                string panelName = "Family Tools";
                RibbonPanel ribbonPanel = application.CreateRibbonPanel(tabName, panelName);

                // Informações do assembly
                string assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;

                // Criar botão Family Export
                PushButtonData buttonData = new PushButtonData(
                    "FamilyExportButton",
                    "Family Export",
                    assemblyPath,
                    "BimexDeveloperPlugin.FamilyExportCommand");

                buttonData.ToolTip = "Cria um novo modelo, adiciona piso 5x5, carrega família na origem e remove o piso";
                buttonData.LongDescription = "Esta ferramenta cria um novo modelo do zero, adiciona um piso 5x5 centralizado na origem, permite selecionar e carregar uma família na origem, e depois remove o piso deixando apenas a família.";

                // Adicionar o botão ao painel
                PushButton pushButton = ribbonPanel.AddItem(buttonData) as PushButton;

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao inicializar plugin: {ex.Message}");
                return Result.Failed;
            }
        }

        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
