# BIMEX Developer Plugin

Este plugin adiciona uma nova aba "BIMEX Developer" no Revit com ferramentas específicas para desenvolvedores.

## Funcionalidades

### Family Export
Esta ferramenta executa automaticamente o seguinte processo:

1. **Criar novo modelo**: Cria um novo documento Revit do zero
2. **Criar piso 5x5**: Adiciona um piso de 5x5 metros centralizado na origem (0,0,0)
3. **Selecionar família**: Abre um seletor de arquivo para escolher uma família (.rfa)
4. **Inserir família**: Carrega e posiciona a família na origem
5. **Remover piso**: Apaga o piso, deixando apenas a família no modelo

## Instalação

1. Compile o projeto para gerar o arquivo `BimexDeveloperPlugin.dll`
2. Co<PERSON> os seguintes arquivos para a pasta de Add-ins do Revit:
   - `BimexDeveloperPlugin.dll`
   - `BimexDeveloperPlugin.addin`

### Pasta de Add-ins do Revit:
- **Revit 2024**: `C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2024\`
- **Revit 2023**: `C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2023\`

## Como Usar

1. Abra o Revit
2. Procure pela aba "BIMEX Developer" na ribbon
3. No painel "Family Tools", clique no botão "Family Export"
4. Siga as instruções na tela:
   - Um novo modelo será criado automaticamente
   - Um piso temporário será adicionado
   - Selecione o arquivo .rfa da família desejada
   - A família será carregada na origem
   - O piso será removido automaticamente

## Requisitos

- Revit 2024 (ou ajuste o caminho das DLLs no arquivo .csproj)
- .NET Framework 4.8
- Windows Forms

## Estrutura do Projeto

```
plugin-revit-developer/
├── BimexDeveloperPlugin.addin     # Arquivo de configuração do add-in
├── BimexDeveloperPlugin.csproj    # Arquivo de projeto
├── FamilyExportCommand.cs         # Código principal do plugin
└── README.md                      # Este arquivo
```

## Desenvolvimento

Para modificar ou estender o plugin:

1. Abra o arquivo `BimexDeveloperPlugin.csproj` no Visual Studio
2. Certifique-se de que os caminhos para `RevitAPI.dll` e `RevitAPIUI.dll` estão corretos
3. Faça suas modificações no arquivo `FamilyExportCommand.cs`
4. Compile o projeto
5. Copie os arquivos gerados para a pasta de Add-ins do Revit

## Troubleshooting

### Plugin não aparece no Revit
- Verifique se os arquivos estão na pasta correta de Add-ins
- Confirme se o arquivo .addin está configurado corretamente
- Verifique se a DLL foi compilada para a arquitetura correta (x64)

### Erro ao carregar família
- Certifique-se de que o arquivo selecionado é uma família Revit válida (.rfa)
- Verifique se a família não está corrompida
- Confirme se você tem permissões para acessar o arquivo

### Erro ao criar piso
- Verifique se existe um tipo de piso padrão no template
- Confirme se o nível ativo está disponível

## Suporte

Para suporte técnico, entre em contato com a equipe BIMEX.
