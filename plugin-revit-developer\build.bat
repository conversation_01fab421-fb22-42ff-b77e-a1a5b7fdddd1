@echo off
echo ========================================
echo BIMEX Developer Plugin - Build Script
echo ========================================

REM Verificar se o dotnet está instalado
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: .NET SDK não encontrado. Instale o .NET SDK primeiro.
    pause
    exit /b 1
)

echo Compilando o projeto...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilação.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Compilação concluída com sucesso!
echo ========================================
echo.
echo Arquivos gerados em: bin\Release\net48\
echo.
echo Para instalar o plugin:
echo 1. Copie BimexDeveloperPlugin.dll para a pasta de Add-ins do Revit
echo 2. Copie BimexDeveloperPlugin.addin para a mesma pasta
echo.
echo Pasta de Add-ins do Revit 2024:
echo %APPDATA%\Autodesk\Revit\Addins\2024\
echo.
pause
