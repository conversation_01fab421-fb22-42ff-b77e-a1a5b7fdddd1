import type { Config } from "tailwindcss"

const config = {
  darkMode: "class",
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        display: ["var(--font-playfair)"],
        serif: ["var(--font-cormorant)"],
        sans: ["var(--font-inter)"],
        inter: ["var(--font-inter)"],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },      letterSpacing: {
        luxury: "0.025em",
        premium: "0.05em",
        exclusive: "0.1em",
      },
      fontSize: {
        // Display sizes (Playfair Display)
        'display-lg': ['4rem', { lineHeight: '1.1', letterSpacing: '0.025em' }],     // 64px
        'display-md': ['3rem', { lineHeight: '1.2', letterSpacing: '0.025em' }],     // 48px
        'display-sm': ['2.5rem', { lineHeight: '1.2', letterSpacing: '0.025em' }],   // 40px
        
        // Heading sizes (Cormorant Garamond)
        'heading-lg': ['2rem', { lineHeight: '1.3', letterSpacing: '0.025em' }],     // 32px
        'heading-md': ['1.5rem', { lineHeight: '1.4', letterSpacing: '0.025em' }],   // 24px
        'heading-sm': ['1.25rem', { lineHeight: '1.4', letterSpacing: '0.025em' }],  // 20px
        
        // Body text sizes (Inter)
        'body-lg': ['1.125rem', { lineHeight: '1.6', letterSpacing: '0.01em' }],     // 18px
        'body-md': ['1rem', { lineHeight: '1.6', letterSpacing: '0.01em' }],         // 16px
        'body-sm': ['0.875rem', { lineHeight: '1.5', letterSpacing: '0.01em' }],     // 14px
        
        // Caption text (Inter)
        'caption': ['0.75rem', { lineHeight: '1.4', letterSpacing: '0.05em' }],      // 12px
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addUtilities }: any) {
      const newUtilities = {
        '.text-display-lg': {
          fontSize: '4rem',
          lineHeight: '1.1',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-playfair)',
          fontWeight: '600',
        },
        '.text-display-md': {
          fontSize: '3rem',
          lineHeight: '1.2',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-playfair)',
          fontWeight: '600',
        },
        '.text-display-sm': {
          fontSize: '2.5rem',
          lineHeight: '1.2',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-playfair)',
          fontWeight: '600',
        },
        '.text-heading-lg': {
          fontSize: '2rem',
          lineHeight: '1.3',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-cormorant)',
          fontWeight: '600',
        },
        '.text-heading-md': {
          fontSize: '1.5rem',
          lineHeight: '1.4',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-cormorant)',
          fontWeight: '600',
        },
        '.text-heading-sm': {
          fontSize: '1.25rem',
          lineHeight: '1.4',
          letterSpacing: '0.025em',
          fontFamily: 'var(--font-cormorant)',
          fontWeight: '600',
        },
        '.text-body-lg': {
          fontSize: '1.125rem',
          lineHeight: '1.6',
          letterSpacing: '0.01em',
          fontFamily: 'var(--font-inter)',
          fontWeight: '400',
        },
        '.text-body-md': {
          fontSize: '1rem',
          lineHeight: '1.6',
          letterSpacing: '0.01em',
          fontFamily: 'var(--font-inter)',
          fontWeight: '400',
        },
        '.text-body-sm': {
          fontSize: '0.875rem',
          lineHeight: '1.5',
          letterSpacing: '0.01em',
          fontFamily: 'var(--font-inter)',
          fontWeight: '400',
        },
        '.text-caption': {
          fontSize: '0.75rem',
          lineHeight: '1.4',
          letterSpacing: '0.05em',
          fontFamily: 'var(--font-inter)',
          fontWeight: '500',
          textTransform: 'uppercase',
        },
        '.tracking-luxury': {
          letterSpacing: '0.025em',
        },
        '.tracking-premium': {
          letterSpacing: '0.05em',
        },
        '.tracking-exclusive': {
          letterSpacing: '0.1em',
        },
      }
      addUtilities(newUtilities)
    }
  ],
} satisfies Config

export default config
