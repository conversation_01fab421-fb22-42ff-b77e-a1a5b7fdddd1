"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Search, Grid, List, Download, Eye, Star, ArrowRight } from "lucide-react"
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

interface Family {
  id: number;
  title: string;
  file_url: string; // Assuming this is the download URL for the family file
  image_url: string; // Assuming this is the URL for the family image
  category: string; // Added based on the new UI
  rating: number; // Added based on the new UI
  downloads: number; // Added based on the new UI
  compatibility: string[]; // Added based on the new UI
  fileSize: string; // Added based on the new UI
  description: string; // Added based on the new UI
  tags: string[]; // Added based on the new UI
  // created_at: string; // This was in the old interface, can be kept if needed
}

// Define the Cloudflare R2 base URL (if still needed, otherwise remove)
// const CLOUDFLARE_BASE_URL = "https://pub-93d9750e41f7494db2c406c9e858c9e6.r2.dev";

// Helper function to construct full image URL (if still needed, otherwise remove)
// const getFullImageUrl = (imagePath: string): string => {
//   if (!imagePath) {
//     return '';
//   }
//   if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
//     return imagePath;
//   }
//   const baseUrl = CLOUDFLARE_BASE_URL.endsWith('/') ? CLOUDFLARE_BASE_URL.slice(0, -1) : CLOUDFLARE_BASE_URL;
//   const path = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;
//   return `${baseUrl}/${path}`;
// };

const categories = ["All", "Furniture", "Lighting", "Fixtures", "Equipment"]; // Example categories
const sortOptions = ["Most Popular", "Newest", "Highest Rated", "Most Downloaded"]; // Example sort options

export default function BimexCatalog() {
  const [families, setFamilies] = useState<Family[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("Most Popular")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  useEffect(() => {
    async function fetchFamilies() {
      try {
        setLoading(true);
        // const response = await fetch('/api/families'); // Original fetch
        // Replace with your actual API endpoint if different
        const response = await fetch('/api/families');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        // Ensure the data matches the new Family interface
        // You might need to transform the data here if the API response is different
        setFamilies(data.map((family: any) => ({
          ...family,
          // Ensure all fields from the new Family interface are present
          // Add default/fallback values if necessary
          category: family.category || "Unknown",
          rating: family.rating || 0,
          downloads: family.downloads || 0,
          compatibility: family.compatibility || [],
          fileSize: family.fileSize || "N/A",
          description: family.description || "No description available.",
          tags: family.tags || [],
        })));
        setError(null);
      } catch (e: any) {
        console.error("Fetch error:", e);
        setError(e.message || "Failed to load families. Check console for details.");
      } finally {
        setLoading(false);
      }
    }

    fetchFamilies();
  }, []);


  const filteredFamilies = families.filter((family) => {
    const matchesSearch =
      family.title.toLowerCase().includes(searchTerm.toLowerCase()) || // Changed from name to title
      family.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "All" || family.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // Sorting logic (example, adjust as needed)
  const sortedFamilies = [...filteredFamilies].sort((a, b) => {
    if (sortBy === "Most Popular" || sortBy === "Most Downloaded") {
      return b.downloads - a.downloads;
    }
    if (sortBy === "Highest Rated") {
      return b.rating - a.rating;
    }
    if (sortBy === "Newest") {
      // Assuming you have a date field like 'created_at' or similar
      // return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      return 0; // Placeholder if no date field
    }
    return 0;
  });


  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen font-sans text-neutral-700">
        <p>Carregando famílias...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-5 font-sans text-red-600">
        <h1 className="text-xl font-bold">Erro ao Carregar</h1>
        <p>{error}</p>
        <p>Verifique se o servidor Next.js (http://localhost:3000) está rodando e acessível.</p>
        <p>Abra as DevTools (pressione F12 se habilitado no plugin) para mais detalhes.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row gap-6 items-center justify-between mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center w-full lg:w-auto">
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search families..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className={cn("whitespace-nowrap", selectedCategory === category ? "bg-primary" : "")}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex border border-gray-300 rounded-md">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className={cn("rounded-r-none", viewMode === "grid" ? "bg-primary" : "")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className={cn("rounded-l-none", viewMode === "list" ? "bg-primary" : "")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Results count */}
        <div className="mb-6">
          <p className="text-sm text-gray-600">
            Showing {sortedFamilies.length} of {families.length} families
          </p>
        </div>

        {/* No results message */}
        {sortedFamilies.length === 0 && !loading && (
          <div className="text-center py-10">
            <p className="text-xl text-gray-500">No families found matching your criteria.</p>
          </div>
        )}

        {/* Grid View */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedFamilies.map((family) => (
              <Card key={family.id} className="group hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-0">
                  {/* Changed structure: The outer div is no longer a Link itself. Link wraps only the image area. */}
                  <div className="relative overflow-hidden rounded-t-lg"> 
                    <Link href={`/families-plugin/${family.id}`} passHref>
                      <Image
                        src={family.image_url || "/placeholder.svg"}
                        alt={family.title}
                        width={300}
                        height={200}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300 cursor-pointer"
                        unoptimized
                      />
                    </Link>
                    {/* Buttons are now absolutely positioned but not children of the Image's Link in terms of DOM nesting of <a> tags */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                      <div className="flex gap-2">
                        <Button asChild size="icon" variant="secondary" className="h-8 w-8 p-0 backdrop-blur-sm">
                          <Link href={`/families-plugin/${family.id}`} aria-label={`Preview ${family.title}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button asChild size="icon" variant="secondary" className="h-8 w-8 p-0 backdrop-blur-sm">
                          <a href={family.file_url} download target="_blank" rel="noopener noreferrer" aria-label={`Download ${family.title}`}>
                            <Download className="h-4 w-4" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <Link href={`/families-plugin/${family.id}`} passHref>
                        <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2 cursor-pointer">
                          {family.title}
                        </h3>
                      </Link>
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>{family.rating.toFixed(1)}</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {family.description}
                    </p>
                    <div className="flex items-center justify-between mb-3">
                      <Badge variant="secondary" className="text-xs">
                        {family.category}
                      </Badge>
                      <span className="text-xs text-gray-500">{family.fileSize}</span>
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{family.downloads.toLocaleString()} downloads</span>
                      <span>{family.compatibility.length > 0 ? `${family.compatibility.length} versions` : '-'}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          /* List View */
          <div className="space-y-4">
            {sortedFamilies.map((family) => (
              <Card key={family.id} className="hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex flex-col sm:flex-row gap-6">
                    <Link href={`/families-plugin/${family.id}`} passHref>
                      <div className="relative w-full sm:w-24 h-24 flex-shrink-0 cursor-pointer">
                        <Image
                          src={family.image_url || "/placeholder.svg"} // Use image_url
                          alt={family.title} // Use title
                          layout="fill"
                          objectFit="cover"
                          className="rounded-md"
                          unoptimized
                        />
                      </div>
                    </Link>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row items-start justify-between mb-2">
                        <Link href={`/families-plugin/${family.id}`} passHref>
                          <h3 className="text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors cursor-pointer">{family.title}</h3>
                        </Link>
                        <div className="flex items-center gap-6 mt-2 sm:mt-0">
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span>{family.rating.toFixed(1)}</span>
                          </div>
                          <div className="flex gap-2">
                            <Button asChild size="sm" variant="outline">
                              <Link href={`/families-plugin/${family.id}`}>
                                <Eye className="h-4 w-4 mr-2" />
                                Preview
                              </Link>
                            </Button>
                            <Button asChild size="sm" variant="default">
                              <a href={family.file_url} download target="_blank" rel="noopener noreferrer">
                                <Download className="h-4 w-4 mr-2" />
                                Download
                              </a>
                            </Button>
                          </div>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-3 line-clamp-3">
                        {family.description}
                      </p>
                      <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-gray-500">
                        <Badge variant="secondary">{family.category}</Badge>
                        <span>{family.fileSize}</span>
                        <span>{family.downloads.toLocaleString()} downloads</span>
                        <span>
                          Compatible with {family.compatibility.length > 0 ? family.compatibility.join(', ') : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Optional: If you have a separate Loading component, you can define it here or import it.
// export function Loading() {
//   return (
//     <div className="flex justify-center items-center h-screen">
//       <p className="text-lg">Loading catalog...</p>
//     </div>
//   );
// }
