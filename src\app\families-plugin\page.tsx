"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Search, Grid, List, Download, Eye, Star, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

interface Family {
  id: number;
  title: string;
  file_url: string;
  image_url: string;
  category: string;
  rating: number;
  downloads: number;
  compatibility: string[];
  fileSize: string;
  description: string;
  tags: string[];
  manufacturer: string; // Added manufacturer
}

const categories = ["All", "Furniture", "Lighting", "Fixtures", "Equipment"]
const sortOptions = ["Most Popular", "Newest", "Highest Rated", "Most Downloaded"]

export default function BimexCatalog() {
  const [families, setFamilies] = useState<Family[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("Most Popular")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  useEffect(() => {
    async function fetchFamilies() {
      try {
        setLoading(true);
        const response = await fetch('/api/families');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setFamilies(data.map((family: any) => ({
          ...family,
          category: family.category || "Unknown",
          rating: family.rating || 0,
          downloads: family.downloads || 0,
          compatibility: family.compatibility || [],
          fileSize: family.fileSize || "N/A",
          description: family.description || "No description available.",
          tags: family.tags || [],
          manufacturer: family.manufacturer || "Unknown", // Added manufacturer mapping
        })));
        setError(null);
      } catch (e: any) {
        console.error("Fetch error:", e);
        setError(e.message || "Failed to load families. Check console for details.");
      } finally {
        setLoading(false);
      }
    }

    fetchFamilies();
  }, []);

  const filteredFamilies = families.filter((family) => {
    const matchesSearch =
      family.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      family.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "All" || family.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // Sorting logic - assuming 'name' for title to match reference
  const sortedFamilies = [...filteredFamilies].sort((a, b) => {
    if (sortBy === "Most Popular" || sortBy === "Most Downloaded") {
      return b.downloads - a.downloads;
    }
    if (sortBy === "Highest Rated") {
      return b.rating - a.rating;
    }
    if (sortBy === "Newest") {
      // Assuming you might add a date field later, for now, no change or sort by ID/name
      return 0; // Placeholder if no date field
    }
    // Default sort or handle other cases
    return 0;
  });
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-body-md text-gray-600 font-serif">Curating premium families...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-display-md text-black mb-4">Unable to Load Catalog</h1>
          <p className="text-body-md text-gray-600 font-serif mb-8">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="bg-black text-white hover:bg-gray-800 text-caption tracking-premium"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">      {/* Header */}
      <header className="border-b border-gray-200 bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-12">              <h1 className="text-4xl font-display font-bold text-black">BIMEX</h1>
              <nav className="hidden md:flex space-x-10">
                <a
                  href="#"
                  className="text-caption text-gray-900 hover:text-gray-600 transition-colors"
                >
                  Catalog
                </a>
                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Collections
                </a>
                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors"
                >
                  About
                </a>
                <a
                  href="#"
                  className="text-caption text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Support
                </a>
              </nav>
            </div>            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" className="text-caption">
                Sign In
              </Button>
              <Button size="sm" className="bg-black text-white hover:bg-gray-800 text-caption">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </header>      {/* Hero Section */}
      <section className="bg-gray-50 py-20 lg:py-28">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-display-lg text-black mb-8 max-w-4xl mx-auto">Premium Revit Families</h2>
          <p className="text-body-lg text-gray-600 mb-12 max-w-3xl mx-auto font-serif">
            Discover our curated collection of high-quality Revit families, meticulously crafted for architectural
            excellence and design sophistication.
          </p>          <div className="flex flex-wrap justify-center items-center gap-4 sm:gap-8 text-caption text-gray-500">
            {/* Updated to use families.length to match actual data */}
            <span>{families.length}+ Families</span>
            <Separator orientation="vertical" className="h-4 hidden sm:block" />
            <span>Weekly Updates</span>
            <Separator orientation="vertical" className="h-4 hidden sm:block" />
            <span>Premium Quality</span>
          </div>
        </div>
      </section>

      {/* Featured Quote */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <blockquote className="text-heading-md text-gray-800 italic max-w-4xl mx-auto mb-6">
            "Excellence is never an accident. It is always the result of high intention, sincere effort, and intelligent
            execution."
          </blockquote>
          <cite className="text-caption text-gray-500">— ARISTOTLE</cite>
        </div>
      </section>      {/* Filters and Search */}
      <section className="border-b border-gray-200 bg-white sticky top-20 z-40">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-center w-full lg:w-auto">
              <div className="relative w-full sm:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search families..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-300 focus:border-black text-body-sm"
                />
              </div>              <div className="flex gap-2 overflow-x-auto pb-2 sm:pb-0">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={`text-caption whitespace-nowrap ${
                      selectedCategory === category ? "bg-black text-white" : ""
                    }`}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>            <div className="flex items-center gap-4 w-full sm:w-auto">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-40 text-body-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option} value={option} className="text-body-sm">
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="flex border border-gray-300 rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={`rounded-r-none ${viewMode === "grid" ? "bg-black text-white" : ""}`}
                  aria-label="Grid view"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={`rounded-l-none ${viewMode === "list" ? "bg-black text-white" : ""}`}
                  aria-label="List view"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Results */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <p className="text-body-sm text-gray-600 font-serif">
              Showing {sortedFamilies.length} of {families.length} families
            </p>
          </div>

          {/* No results message */}
          {sortedFamilies.length === 0 && !loading && (
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <h3 className="text-heading-lg text-gray-900 mb-4">No families found</h3>
                <p className="text-body-md text-gray-600 font-serif mb-8">
                  We couldn't find any families matching your criteria. Try adjusting your search or filters.
                </p>                <Button 
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("All")
                  }}
                  variant="outline" 
                  className="text-caption"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          )}          {/* Results */}
          {sortedFamilies.length > 0 && (
            <>
              {viewMode === "grid" ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
                  {sortedFamilies.map((family) => (                    <Card
                      key={family.id}
                      className="group border-gray-200 overflow-hidden"
                    >
                      <CardContent className="p-0">                        <div className="relative overflow-hidden bg-gray-50">
                          <Link href={`/families-plugin/${family.id}`}>
                            <img
                              src={family.image_url || "/placeholder.svg"}
                              alt={family.title}
                              className="w-full h-48 sm:h-56 object-contain cursor-pointer"
                            />
                          </Link>
                        </div>
                        <div className="p-4 sm:p-6">                          <div className="flex items-start justify-between mb-3">
                            <Link href={`/families-plugin/${family.id}`}>
                              <h3 className="text-heading-sm text-gray-900 cursor-pointer line-clamp-2">
                                {family.title}
                              </h3>
                            </Link>
                            <div className="flex items-center gap-1 text-body-sm text-gray-500 ml-2">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="font-serif">{family.rating}</span>
                            </div>
                          </div>
                          {/* Removed description from grid view to match reference */}                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="text-caption">
                              {family.manufacturer}
                            </Badge>
                            <Button
                              asChild
                              size="sm"
                              className="bg-black text-white hover:bg-gray-800 text-caption"
                            >
                              <a href={family.file_url} download target="_blank" rel="noopener noreferrer">
                                <Download className="h-4 w-4 mr-2" />
                                Download
                              </a>
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (                <div className="space-y-6">
                  {sortedFamilies.map((family) => (
                    <Card key={family.id} className="border-gray-200">
                      <CardContent className="p-6 sm:p-8">
                        <div className="flex flex-col sm:flex-row gap-6 sm:gap-8">                          <img
                            src={family.image_url || "/placeholder.svg"}
                            alt={family.title}
                            className="w-full sm:w-32 h-48 sm:h-32 object-cover flex-shrink-0"
                          />
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row items-start justify-between mb-3">
                              <h3 className="text-heading-md text-gray-900 mb-2 sm:mb-0">{family.title}</h3>
                              <div className="flex items-center gap-6">
                                <div className="flex items-center gap-1 text-body-sm text-gray-500">
                                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                  <span className="font-serif">{family.rating}</span>
                                </div>
                                <div className="flex gap-3">                                  <Button size="sm" variant="outline" className="text-caption">
                                    <Eye className="h-4 w-4 mr-2" />
                                    Preview
                                  </Button>
                                  <Button
                                    size="sm"
                                    className="bg-black text-white hover:bg-gray-800 text-caption"
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <p className="text-body-md text-gray-600 mb-4 font-serif leading-relaxed">
                              {family.description}
                            </p>                            <div className="flex flex-wrap items-center gap-4 text-caption text-gray-500">
                              <Badge variant="secondary">{family.manufacturer}</Badge>
                              <span>{family.fileSize}</span>
                              <span>{family.downloads.toLocaleString()} downloads</span>
                              <span>Compatible with {family.compatibility.length} versions</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-display-md text-white mb-6">Elevate Your Designs</h2>
          <p className="text-body-lg text-gray-300 mb-8 max-w-2xl mx-auto font-serif">
            Join thousands of architects and designers who trust BIMEX for their premium Revit family needs.
          </p>          <Button
            size="lg"
            variant="outline"
            className="text-caption border-white text-white hover:bg-white hover:text-black"
          >
            Explore Premium Collection
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div>
              <h3 className="text-display-sm text-white mb-6">BIMEX</h3>
              <p className="text-body-sm text-gray-400 font-serif leading-relaxed">
                Premium Revit families for architectural excellence and design sophistication.
              </p>
            </div>
            <div>
              <h4 className="text-heading-sm text-white mb-6">Product</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Catalog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Collections
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    New Releases
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-heading-sm text-white mb-6">Support</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-heading-sm text-white mb-6">Company</h4>
              <ul className="space-y-3 text-body-sm text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors font-serif">
                    Privacy
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <Separator className="my-12 bg-gray-700" />
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-body-sm text-gray-400 font-serif">© 2024 BIMEX. All rights reserved.</p>            <div className="flex space-x-8 mt-6 md:mt-0">
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors">
                Terms
              </a>
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors">
                Privacy
              </a>
              <a href="#" className="text-caption text-gray-400 hover:text-white transition-colors">
                Cookies
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
